import * as z from "zod";

// Common error response schema
export const ErrorResponseSchema = z.object({
  success: z.boolean().default(false),
  error: z.string(),
  message: z.string().optional(),
  code: z.string().optional(),
  details: z.any().optional()
});

// Success response schema
export const SuccessResponseSchema = z.object({
  success: z.boolean().default(true),
  message: z.string(),
  data: z.any().optional()
});

// Pagination schema
export const PaginationSchema = z.object({
  total: z.number(),
  limit: z.number(),
  offset: z.number(),
  hasMore: z.boolean()
});

// Common query parameters
export const CommonQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(100).optional(),
  offset: z.coerce.number().min(0).optional()
});

// Health check response schema
export const HealthResponseSchema = z.object({
  status: z.string(),
  timestamp: z.iso.datetime(),
  environment: z.string(),
  version: z.string().optional(),
  uptime: z.number().optional()
});

// User ID parameter schema
export const UserParamsSchema = z.object({
  userId: z.string()
});

// Date range query schema
export const DateRangeQuerySchema = z.object({
  from: z.iso.datetime().optional(),
  to: z.iso.datetime().optional()
});

// Export types
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;
export type CommonQuery = z.infer<typeof CommonQuerySchema>;
export type HealthResponse = z.infer<typeof HealthResponseSchema>;
export type UserParams = z.infer<typeof UserParamsSchema>;
export type DateRangeQuery = z.infer<typeof DateRangeQuerySchema>;
