// Production-ready Telegraf bot core with advanced patterns

import { Context } from "hono";
import { Telegraf, session, Scenes, Composer } from "telegraf";
import { RepositoryFactory } from "@/repositories";
import { AIService } from "@/ai/ai.service";
import { TaskService } from "@/services/task.service";
import { NotificationService } from "@/services/notification.service";
import { BirthdayService } from "@/services/birthday.service";
import { EventService } from "@/services/event.service";
import { IntegrationService } from "@/services/integration.service";
import { TBotContext, TSession, BotError, BotErrorType } from "./types";
import { SessionManager } from "./session";

export interface BotServices {
  aiService: AIService;
  taskService: TaskService;
  notificationService: NotificationService;
  birthdayService: BirthdayService;
  eventService: EventService;
  integrationService: IntegrationService;
  repositoryFactory: RepositoryFactory;
  sessionManager: SessionManager;
}

export class BotCore {
  private bot: Telegraf<TBotContext>;
  private services: BotServices;
  private stage: Scenes.Stage<TBotContext>;

  constructor(botToken: string, env: any) {
    // Initialize repository factory
    const repositoryFactory = new RepositoryFactory(env.BOT_DB);

    // Initialize session manager
    const sessionManager = new SessionManager(repositoryFactory);

    // Initialize AI service
    const aiService = new AIService(env.AI);

    // Initialize services
    const taskService = new TaskService(repositoryFactory, aiService);
    const notificationService = new NotificationService(repositoryFactory);
    const birthdayService = new BirthdayService(repositoryFactory);
    const eventService = new EventService(repositoryFactory);
    const integrationService = new IntegrationService(repositoryFactory);

    this.services = {
      aiService,
      taskService,
      notificationService,
      birthdayService,
      eventService,
      integrationService,
      repositoryFactory,
      sessionManager
    };

    // Initialize Telegraf bot with scenes support
    this.bot = new Telegraf<TBotContext>(botToken);

    // Initialize stage for scenes
    this.stage = new Scenes.Stage<TBotContext>([]);

    this.setupMiddleware();
  }

  /**
   * Setup comprehensive middleware stack
   */
  private setupMiddleware(): void {
    // Error handling middleware (first in chain)
    this.bot.use(this.errorHandlingMiddleware.bind(this));

    // Session middleware with persistent storage
    this.bot.use(session({
      defaultSession: (): TSession => ({} as TSession)
    }));

    // Initialize user session
    this.bot.use(this.sessionInitializationMiddleware.bind(this));

    // Rate limiting middleware
    this.bot.use(this.rateLimitingMiddleware.bind(this));

    // Analytics middleware
    this.bot.use(this.analyticsMiddleware.bind(this));

    // Scene middleware
    this.bot.use(this.stage.middleware());

    // User tier validation middleware
    this.bot.use(this.userTierMiddleware.bind(this));
  }

  /**
   * Error handling middleware with comprehensive error management
   */
  private async errorHandlingMiddleware(ctx: TBotContext, next: () => Promise<void>): Promise<void> {
    try {
      await next();
    } catch (error: any) {
      console.error("Bot error:", error);

      const botError = this.classifyError(error);
      await this.handleBotError(ctx, botError);
    }
  }

  /**
   * Session initialization middleware
   */
  private async sessionInitializationMiddleware(ctx: TBotContext, next: () => Promise<void>): Promise<void> {
    await this.services.sessionManager.initializeSession(ctx);
    await next();
  }

  /**
   * Rate limiting middleware
   */
  private async rateLimitingMiddleware(ctx: TBotContext, next: () => Promise<void>): Promise<void> {
    const isAllowed = await this.services.sessionManager.checkRateLimit(ctx);

    if (!isAllowed) {
      const error: BotError = {
        type: BotErrorType.RATE_LIMIT_EXCEEDED,
        message: "Rate limit exceeded",
        retryable: true,
        userMessage: "⚠️ You're sending messages too quickly. Please wait a moment before trying again."
      };

      await this.handleBotError(ctx, error);
      return;
    }

    await next();
  }

  /**
   * Analytics middleware
   */
  private async analyticsMiddleware(ctx: TBotContext, next: () => Promise<void>): Promise<void> {
    const startTime = Date.now();

    // Determine action type
    let action = "unknown";
    if (ctx.message && "text" in ctx.message) {
      action = ctx.message.text.startsWith("/") ? "command" : "message";
    } else if (ctx.callbackQuery) {
      action = "callback";
    }

    await this.services.sessionManager.updateAnalytics(ctx, action);

    await next();

    // Log performance metrics
    const duration = Date.now() - startTime;
    console.log(`Action: ${action}, Duration: ${duration}ms, User: ${ctx.from?.id}`);
  }

  /**
   * User tier validation middleware
   */
  private async userTierMiddleware(ctx: TBotContext, next: () => Promise<void>): Promise<void> {
    // Check if user is blocked
    if (ctx.session?.user?.isBlocked) {
      await ctx.reply(
        `🚫 Your account has been temporarily blocked.\nReason: ${ctx.session.user.blockReason || "Violation of terms"}\n\nContact support if you believe this is an error.`
      );
      return;
    }

    await next();
  }

  /**
   * Create role-based composers for access control
   */
  createRoleBasedComposers(): {
    adminComposer: Composer<TBotContext>;
    paidComposer: Composer<TBotContext>;
    freeComposer: Composer<TBotContext>;
  } {
    const adminComposer = new Composer<TBotContext>();
    const paidComposer = new Composer<TBotContext>();
    const freeComposer = new Composer<TBotContext>();

    // Admin access control
    adminComposer.use((ctx, next) => {
      if (ctx.session?.user?.tier.type === "admin") {
        return next();
      }
      return this.handleUnauthorizedAccess(ctx);
    });

    // Paid user access control
    paidComposer.use((ctx, next) => {
      const userType = ctx.session?.user?.tier.type;
      if (userType === "admin" || userType === "paid") {
        return next();
      }
      return this.handlePaymentRequired(ctx);
    });

    // Free user access control (everyone)
    freeComposer.use((ctx, next) => next());

    return { adminComposer, paidComposer, freeComposer };
  }

  /**
   * Add scene to the stage
   */
  addScene(scene: Scenes.BaseScene<TBotContext> | Scenes.WizardScene<TBotContext>): void {
    this.stage.register(scene);
  }

  /**
   * Get the bot instance
   */
  getBot(): Telegraf<TBotContext> {
    return this.bot;
  }

  /**
   * Get services
   */
  getServices(): BotServices {
    return this.services;
  }

  /**
   * Handle webhook requests
   */
  async handleWebhook(c: Context): Promise<Response> {
    try {
      const body = await c.req.json();
      console.log("Received Telegram webhook:", JSON.stringify(body, null, 2));

      // Process the Telegram message using Telegraf
      await this.bot.handleUpdate(body);

      return c.json({ status: "ok" });
    } catch (error: any) {
      console.error("Error processing Telegram webhook:", error);
      return c.json({ status: "error", message: error.message }, 500);
    }
  }

  /**
   * Classify errors for appropriate handling
   */
  private classifyError(error: any): BotError {
    if (error.code === 429) {
      return {
        type: BotErrorType.RATE_LIMIT_EXCEEDED,
        message: "Rate limit exceeded",
        retryable: true,
        userMessage: "⚠️ Too many requests. Please try again later."
      };
    }

    if (error.message?.includes("quota")) {
      return {
        type: BotErrorType.QUOTA_EXCEEDED,
        message: "Quota exceeded",
        retryable: false,
        userMessage: "📊 You've reached your usage limit. Consider upgrading your plan."
      };
    }

    if (error.code >= 400 && error.code < 500) {
      return {
        type: BotErrorType.INVALID_INPUT,
        message: "Invalid input",
        retryable: false,
        userMessage: "❌ Invalid input. Please check your request and try again."
      };
    }

    if (error.code >= 500) {
      return {
        type: BotErrorType.SERVICE_UNAVAILABLE,
        message: "Service unavailable",
        retryable: true,
        userMessage: "🔧 Service temporarily unavailable. Please try again later."
      };
    }

    return {
      type: BotErrorType.INTERNAL_ERROR,
      message: error.message || "Unknown error",
      retryable: true,
      userMessage: "❌ Something went wrong. Please try again."
    };
  }

  /**
   * Handle bot errors with appropriate user feedback
   */
  private async handleBotError(ctx: TBotContext, error: BotError): Promise<void> {
    try {
      // Send user-friendly error message
      await ctx.reply(error.userMessage);

      // Log error for monitoring
      console.error("Bot Error:", {
        type: error.type,
        message: error.message,
        userId: ctx.from?.id,
        details: error.details
      });

      // Handle specific error types
      switch (error.type) {
        case BotErrorType.QUOTA_EXCEEDED:
          await this.handleQuotaExceeded(ctx);
          break;
        case BotErrorType.PAYMENT_REQUIRED:
          await this.handlePaymentRequired(ctx);
          break;
        case BotErrorType.RATE_LIMIT_EXCEEDED:
          // Rate limit handling is done in middleware
          break;
      }
    } catch (errorHandlingError) {
      console.error("Error in error handler:", errorHandlingError);
    }
  }

  /**
   * Handle quota exceeded scenarios
   */
  private async handleQuotaExceeded(ctx: TBotContext): Promise<void> {
    const upgradeMessage = `
🚀 *Upgrade to Premium*

You've reached your usage limit. Upgrade to get:
• Unlimited tasks and birthdays
• Enhanced AI features
• Priority support
• Export capabilities

Tap /upgrade to learn more!
    `;

    await ctx.reply(upgradeMessage, { parse_mode: "Markdown" });
  }

  /**
   * Handle payment required scenarios
   */
  private async handlePaymentRequired(ctx: TBotContext): Promise<void> {
    const premiumMessage = `
💎 *Premium Feature*

This feature is available for Premium users only.

Premium benefits:
• Unlimited everything
• Advanced AI capabilities
• Priority support
• Custom themes

Tap /upgrade to unlock all features!
    `;

    await ctx.reply(premiumMessage, { parse_mode: "Markdown" });
  }

  /**
   * Handle unauthorized access
   */
  private async handleUnauthorizedAccess(ctx: TBotContext): Promise<void> {
    await ctx.reply("🔒 You don't have permission to access this feature.");
  }
}
