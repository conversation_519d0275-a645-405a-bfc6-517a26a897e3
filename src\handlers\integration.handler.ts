/**
 * Integration Handler
 * Handles Telegram bot commands for external integrations
 */

import { Context as TelegrafContext } from "telegraf";
import { Markup } from "telegraf";
import { IntegrationService } from "../services/integration.service";

// Define Bot<PERSON>ontext interface to match the one in telegram-bot.ts
interface BotContext extends TelegrafContext {
  session?: {
    awaitingTaskTitle?: boolean;
    awaitingBirthdayName?: boolean;
    awaitingEventName?: boolean;
    awaitingInput?: string;
    searchMode?: string;
    tempTaskData?: any;
    tempBirthdayData?: any;
    tempEventData?: any;
    currentPage?: number;
    currentFilter?: string;
    currentMenu?: string;
    editingTaskId?: string;
    editingBirthdayId?: string;
  };
}

export class IntegrationHandler {
  constructor(private integrationService: IntegrationService) { }

  /**
   * Handle /search command
   */
  async handleSearchCommand(ctx: BotContext): Promise<void> {
    try {
      const args = (ctx.message && "text" in ctx.message && ctx.message.text) ? ctx.message.text.split(" ").slice(1) : [];

      if (!args || args.length === 0) {
        await ctx.reply(
          "🔍 *Search Command*\n\n" +
          "Use: `/search <query>` to search across all sources\n" +
          "Or use the buttons below for specific searches:",
          {
            parse_mode: "Markdown",
            reply_markup: Markup.inlineKeyboard([
              [
                Markup.button.callback("🌐 Web Search", "search_web"),
                Markup.button.callback("📝 Task Search", "search_tasks")
              ],
              [
                Markup.button.callback("📚 Knowledge Search", "search_knowledge"),
                Markup.button.callback("🔍 All Sources", "search_all")
              ],
              [Markup.button.callback("📊 Search Stats", "search_stats")]
            ]).reply_markup
          }
        );
        return;
      }

      const query = args.join(" ");
      await this.performSearch(ctx, query, "all");

    } catch (error) {
      console.error("Search command error:", error);
      await ctx.reply("❌ Search failed. Please try again.");
    }
  }

  /**
   * Handle search callback queries
   */
  async handleSearchCallback(ctx: BotContext): Promise<void> {
    try {
      const callbackData = (ctx.callbackQuery && "data" in ctx.callbackQuery) ? ctx.callbackQuery.data : undefined;

      if (!callbackData) {
        return;
      }

      if (callbackData === "search_stats") {
        await this.showSearchStats(ctx);
        return;
      }

      // Set search mode in session
      const searchType = callbackData.replace("search_", "");
      if (ctx.session) {
        ctx.session.searchMode = searchType;
      }

      await ctx.editMessageText(
        `🔍 *${this.getSearchTypeTitle(searchType)} Search*\n\n` +
        "Please enter your search query:",
        {
          parse_mode: "Markdown",
          reply_markup: Markup.inlineKeyboard([
            [Markup.button.callback("❌ Cancel", "cancel_search")]
          ]).reply_markup
        }
      );

      if (ctx.session) {
        ctx.session.awaitingInput = "search_query";
      }

    } catch (error) {
      console.error("Search callback error:", error);
      await ctx.answerCbQuery("❌ Search failed");
    }
  }

  /**
   * Handle search query input
   */
  async handleSearchInput(ctx: BotContext): Promise<void> {
    try {
      const query = (ctx.message && "text" in ctx.message) ? ctx.message.text : undefined;
      const searchType = ctx.session?.searchMode || "all";

      if (!query) {
        await ctx.reply("❌ Please enter a valid search query.");
        return;
      }

      // Clear session state
      if (ctx.session) {
        ctx.session.awaitingInput = undefined;
        ctx.session.searchMode = undefined;
      }

      await this.performSearch(ctx, query, searchType);

    } catch (error) {
      console.error("Search input error:", error);
      await ctx.reply("❌ Search failed. Please try again.");
    }
  }

  /**
   * Perform search and display results
   */
  private async performSearch(ctx: BotContext, query: string, type: string): Promise<void> {
    try {
      const userId = ctx.from?.id.toString() || "unknown";

      // Show typing indicator
      await ctx.sendChatAction("typing");

      let searchResult;

      switch (type) {
      case "web":
        searchResult = await this.integrationService.searchWeb(query, userId);
        break;
      case "tasks":
        searchResult = await this.integrationService.searchTasks(query, userId);
        break;
      case "knowledge":
        // Knowledge search not fully implemented yet
        await ctx.reply("📚 Knowledge search coming soon!");
        return;
      default:
        searchResult = await this.integrationService.searchAll(query, userId);
      }

      if (!searchResult || searchResult.results.length === 0) {
        await ctx.reply(
          `🔍 No results found for "${query}"\n\n` +
          "💡 Try:\n" +
          "• Different keywords\n" +
          "• Broader search terms\n" +
          "• Check spelling",
          {
            reply_markup: Markup.inlineKeyboard([
              [Markup.button.callback("🔄 Try Again", "search_" + type)]
            ]).reply_markup
          }
        );
        return;
      }

      const formattedResults = this.integrationService.formatSearchResults(searchResult);

      await ctx.reply(formattedResults, {
        parse_mode: "Markdown",
        reply_markup: Markup.inlineKeyboard([
          [
            Markup.button.callback("🔄 New Search", "search_" + type),
            Markup.button.callback("📊 Stats", "search_stats")
          ]
        ]).reply_markup
      });

    } catch (error) {
      console.error("Perform search error:", error);
      await ctx.reply("❌ Search failed. Please try again.");
    }
  }

  /**
   * Handle /calendar command
   */
  async handleCalendarCommand(ctx: BotContext): Promise<void> {
    try {
      const userId = ctx.from?.id.toString() || "unknown";

      await ctx.reply(
        "📅 *Calendar Integration*\n\n" +
        "Manage your calendar and events:",
        {
          parse_mode: "Markdown",
          reply_markup: Markup.inlineKeyboard([
            [
              Markup.button.callback("📋 Upcoming Events", "cal_upcoming"),
              Markup.button.callback("📊 Overview", "cal_overview")
            ],
            [
              Markup.button.callback("➕ Create Event", "cal_create"),
              Markup.button.callback("🔗 Sync Tasks", "cal_sync_tasks")
            ],
            [
              Markup.button.callback("🎂 Birthday Events", "cal_birthdays"),
              Markup.button.callback("⚙️ Settings", "cal_settings")
            ]
          ]).reply_markup
        }
      );

    } catch (error) {
      console.error("Calendar command error:", error);
      await ctx.reply("❌ Calendar access failed. Please try again.");
    }
  }

  /**
   * Handle calendar callback queries
   */
  async handleCalendarCallback(ctx: BotContext): Promise<void> {
    try {
      const callbackData = (ctx.callbackQuery && "data" in ctx.callbackQuery) ? ctx.callbackQuery.data : undefined;
      const userId = ctx.from?.id.toString() || "unknown";

      if (!callbackData) {
        return;
      }

      switch (callbackData) {
      case "cal_upcoming":
        await this.showUpcomingEvents(ctx, userId);
        break;
      case "cal_overview":
        await this.showCalendarOverview(ctx, userId);
        break;
      case "cal_create":
        await this.startEventCreation(ctx);
        break;
      case "cal_sync_tasks":
        await this.syncTasksToCalendar(ctx, userId);
        break;
      case "cal_birthdays":
        await this.syncBirthdaysToCalendar(ctx, userId);
        break;
      case "cal_settings":
        await this.showCalendarSettings(ctx);
        break;
      default:
        await ctx.answerCbQuery("❌ Unknown calendar action");
      }

    } catch (error) {
      console.error("Calendar callback error:", error);
      await ctx.answerCbQuery("❌ Calendar action failed");
    }
  }

  /**
   * Handle /integrations command
   */
  async handleIntegrationsCommand(ctx: BotContext): Promise<void> {
    try {
      const status = await this.integrationService.getIntegrationStatus();

      let statusText = "🔧 *Integration Status*\n\n";

      if (status && status.integrations) {
        status.integrations.forEach((integration: any) => {
          const emoji = integration.healthy ? "✅" : "❌";
          const enabledText = integration.enabled ? "Enabled" : "Disabled";
          statusText += `${emoji} *${integration.name}*: ${enabledText}\n`;

          if (integration.error) {
            statusText += `   ⚠️ ${integration.error}\n`;
          }
        });
      }

      statusText += "\n🔧 *Available Actions:*";

      await ctx.reply(statusText, {
        parse_mode: "Markdown",
        reply_markup: Markup.inlineKeyboard([
          [
            Markup.button.callback("🔍 Search", "integration_search"),
            Markup.button.callback("📅 Calendar", "integration_calendar")
          ],
          [
            Markup.button.callback("🧪 Test All", "integration_test"),
            Markup.button.callback("📊 Analytics", "integration_analytics")
          ],
          [Markup.button.callback("🔄 Refresh Status", "integration_refresh")]
        ]).reply_markup
      });

    } catch (error) {
      console.error("Integrations command error:", error);
      await ctx.reply("❌ Failed to get integration status.");
    }
  }

  /**
   * Show upcoming events
   */
  private async showUpcomingEvents(ctx: BotContext, userId: string): Promise<void> {
    try {
      const events = await this.integrationService.getUpcomingEvents(userId, 7);

      if (events.length === 0) {
        await ctx.editMessageText(
          "📅 *Upcoming Events*\n\n" +
          "📭 No upcoming events in the next 7 days.\n\n" +
          "💡 Create events from your tasks or add custom events!",
          {
            parse_mode: "Markdown",
            reply_markup: Markup.inlineKeyboard([
              [Markup.button.callback("➕ Create Event", "cal_create")],
              [Markup.button.callback("⬅️ Back", "cal_back")]
            ]).reply_markup
          }
        );
        return;
      }

      let eventsText = "📅 *Upcoming Events (Next 7 Days)*\n\n";

      events.slice(0, 10).forEach((event, index) => {
        const date = new Date(event.startTime).toLocaleDateString();
        const time = event.isAllDay ? "All Day" : new Date(event.startTime).toLocaleTimeString();
        const emoji = this.getEventEmoji(event.metadata?.category);

        eventsText += `${emoji} *${event.title}*\n`;
        eventsText += `📅 ${date} ${time}\n`;

        if (event.description) {
          eventsText += `📝 ${event.description.substring(0, 50)}${event.description.length > 50 ? "..." : ""}\n`;
        }

        eventsText += "\n";
      });

      await ctx.editMessageText(eventsText, {
        parse_mode: "Markdown",
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.callback("📊 Overview", "cal_overview")],
          [Markup.button.callback("⬅️ Back", "cal_back")]
        ]).reply_markup
      });

    } catch (error) {
      console.error("Show upcoming events error:", error);
      await ctx.answerCbQuery("❌ Failed to load events");
    }
  }

  /**
   * Show calendar overview
   */
  private async showCalendarOverview(ctx: BotContext, userId: string): Promise<void> {
    try {
      const overview = await this.integrationService.getCalendarOverview(userId);

      if (!overview) {
        await ctx.answerCbQuery("❌ Failed to load calendar overview");
        return;
      }

      let overviewText = "📊 *Calendar Overview*\n\n";

      if (overview.stats) {
        overviewText += "📈 *Statistics:*\n";
        overviewText += `• Total Events: ${overview.stats.totalEvents || 0}\n`;
        overviewText += `• This Month: ${overview.stats.monthlyEvents || 0}\n`;
        overviewText += `• Upcoming: ${overview.stats.upcomingEvents || 0}\n\n`;
      }

      overviewText += `📅 *Today:* ${overview.todayEvents?.length || 0} events\n`;
      overviewText += `📆 *This Week:* ${overview.thisWeekEvents?.length || 0} events\n\n`;

      if (overview.upcomingEvents && overview.upcomingEvents.length > 0) {
        overviewText += "🔜 *Next Events:*\n";
        overview.upcomingEvents.slice(0, 3).forEach((event: any) => {
          const date = new Date(event.startTime).toLocaleDateString();
          overviewText += `• ${event.title} (${date})\n`;
        });
      }

      await ctx.editMessageText(overviewText, {
        parse_mode: "Markdown",
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.callback("📋 Upcoming", "cal_upcoming")],
          [Markup.button.callback("⬅️ Back", "cal_back")]
        ]).reply_markup
      });

    } catch (error) {
      console.error("Show calendar overview error:", error);
      await ctx.answerCbQuery("❌ Failed to load overview");
    }
  }

  /**
   * Show search statistics
   */
  private async showSearchStats(ctx: BotContext): Promise<void> {
    try {
      // const analytics = await this.integrationService.getSearchAnalytics();
      const analytics = { totalSearches: 0, popularQueries: [] }; // Placeholder

      let statsText = "📊 *Search Statistics*\n\n";
      const enableAnalytics = false;

      if (enableAnalytics) { // Disabled analytics for now
      } else {
        statsText += "📈 Search analytics will appear here as you use the search feature.";
      }

      await ctx.editMessageText(statsText, {
        parse_mode: "Markdown",
        reply_markup: Markup.inlineKeyboard([
          [Markup.button.callback("🔍 New Search", "search_all")],
          [Markup.button.callback("⬅️ Back", "search_back")]
        ]).reply_markup
      });

    } catch (error) {
      console.error("Show search stats error:", error);
      await ctx.answerCbQuery("❌ Failed to load statistics");
    }
  }

  /**
   * Get search type title
   */
  private getSearchTypeTitle(type: string): string {
    switch (type) {
    case "web": return "🌐 Web";
    case "tasks": return "📝 Task";
    case "knowledge": return "📚 Knowledge";
    case "all": return "🔍 Universal";
    default: return "🔍 Search";
    }
  }

  /**
   * Get event emoji based on category
   */
  private getEventEmoji(category?: string): string {
    switch (category) {
    case "task": return "📝";
    case "birthday": return "🎂";
    case "event": return "📅";
    case "meeting": return "👥";
    case "reminder": return "⏰";
    default: return "📅";
    }
  }

  // Placeholder methods for additional functionality
  private async startEventCreation(ctx: BotContext): Promise<void> {
    await ctx.answerCbQuery("📅 Event creation coming soon!");
  }

  private async syncTasksToCalendar(ctx: BotContext, userId: string): Promise<void> {
    await ctx.answerCbQuery("🔗 Task sync coming soon!");
  }

  private async syncBirthdaysToCalendar(ctx: BotContext, userId: string): Promise<void> {
    await ctx.answerCbQuery("🎂 Birthday sync coming soon!");
  }

  private async showCalendarSettings(ctx: BotContext): Promise<void> {
    await ctx.answerCbQuery("⚙️ Calendar settings coming soon!");
  }
}

// Export handler instance (will be created in bot setup)
export let integrationHandler: IntegrationHandler;
