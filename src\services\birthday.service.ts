// Birthday service for managing birthday tracking and reminders

import { RepositoryFactory } from "../repositories";
import type { Birthday } from "@/types";

export class BirthdayService {
  private repositoryFactory: RepositoryFactory;

  constructor(repositoryFactory: RepositoryFactory) {
    this.repositoryFactory = repositoryFactory;
  }

  /**
   * Add a new birthday for a user
   */
  async addBirthday(
    userId: string,
    name: string,
    day: number,
    month: number,
    year?: number,
    reminderDays: number[] = [1, 7, 30]
  ): Promise<Birthday> {
    const birthdayRepo = this.repositoryFactory.getBirthdayRepository();

    // Validate date
    if (day < 1 || day > 31 || month < 1 || month > 12) {
      throw new Error("Invalid date: day must be 1-31, month must be 1-12");
    }

    // Check for leap year if February 29th
    if (month === 2 && day === 29 && year) {
      const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
      if (!isLeapYear) {
        throw new Error("February 29th is only valid in leap years");
      }
    }

    // Check if birthday already exists for this user and name
    const existingBirthdays = await birthdayRepo.findByUserId(userId);
    const duplicate = existingBirthdays.find(b =>
      b.name.toLowerCase() === name.toLowerCase()
    );

    if (duplicate) {
      throw new Error(`Birthday for "${name}" already exists`);
    }

    const birthday = await birthdayRepo.create({
      userId,
      name: name.trim(),
      date: { day, month },
      year,
      reminderDays
    });

    // Schedule notifications for this birthday
    await this.scheduleNotifications(birthday);

    return birthday;
  }

  // Get user birthdays
  async getUserBirthdays(userId: string): Promise<Birthday[]> {
    const birthdayRepo = this.repositoryFactory.getBirthdayRepository();
    return birthdayRepo.findByUserId(userId);
  }

  // Get a specific birthday
  async getBirthday(birthdayId: string, userId: string): Promise<Birthday | null> {
    const birthdayRepo = this.repositoryFactory.getBirthdayRepository();
    const birthday = await birthdayRepo.findById(birthdayId);

    // Ensure the birthday belongs to the user
    if (birthday && birthday.userId === userId) {
      return birthday;
    }

    return null;
  }

  // Get upcoming birthdays
  async getUpcomingBirthdays(userId: string, days: number = 30): Promise<Birthday[]> {
    const birthdayRepo = this.repositoryFactory.getBirthdayRepository();
    return birthdayRepo.findUpcomingBirthdays(userId, days);
  }

  // Get today's birthdays
  async getTodaysBirthdays(userId: string): Promise<Birthday[]> {
    const birthdayRepo = this.repositoryFactory.getBirthdayRepository();
    return birthdayRepo.findBirthdaysOnDate(userId, new Date());
  }

  // Update birthday
  async updateBirthday(
    birthdayId: string,
    userId: string,
    updates: Partial<Birthday>
  ): Promise<Birthday | null> {
    const birthdayRepo = this.repositoryFactory.getBirthdayRepository();

    // First check if the birthday belongs to the user
    const existingBirthday = await this.getBirthday(birthdayId, userId);
    if (!existingBirthday) {
      return null;
    }

    return birthdayRepo.update(birthdayId, updates);
  }

  // Delete birthday
  async deleteBirthday(birthdayId: string, userId: string): Promise<boolean> {
    const birthdayRepo = this.repositoryFactory.getBirthdayRepository();

    // First check if the birthday belongs to the user
    const existingBirthday = await this.getBirthday(birthdayId, userId);
    if (!existingBirthday) {
      return false;
    }

    return birthdayRepo.delete(birthdayId);
  }

  // Calculate age for a birthday
  calculateAge(birthday: Birthday): number | null {
    if (!birthday.year) {
      return null;
    }

    const today = new Date();
    const birthDate = new Date(birthday.year, birthday.date.month - 1, birthday.date.day);

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  }

  // Get next birthday date
  getNextBirthdayDate(birthday: Birthday): Date {
    const today = new Date();
    const currentYear = today.getFullYear();

    let nextBirthday = new Date(currentYear, birthday.date.month - 1, birthday.date.day);

    // If birthday has passed this year, use next year
    if (nextBirthday < today) {
      nextBirthday = new Date(currentYear + 1, birthday.date.month - 1, birthday.date.day);
    }

    return nextBirthday;
  }

  // Get days until next birthday
  getDaysUntilBirthday(birthday: Birthday): number {
    const nextBirthday = this.getNextBirthdayDate(birthday);
    const today = new Date();
    const diffTime = nextBirthday.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Schedule notifications for a birthday
   */
  private async scheduleNotifications(birthday: Birthday): Promise<void> {
    // For now, just log that we would schedule notifications
    // This will be implemented when we integrate with the notification system
    console.log(`Scheduling notifications for ${birthday.name}'s birthday on ${birthday.date.month}/${birthday.date.day}`);
  }

}
