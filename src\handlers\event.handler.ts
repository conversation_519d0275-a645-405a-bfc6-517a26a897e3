import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator as zValidator } from "hono-openapi/zod";
import { RepositoryFactory } from "../repositories";
import { ErrorResponseSchema } from "../schemas/common.schema";
import {
  EventListResponseSchema,
  EventQuerySchema
} from "../schemas/event.schema";
import { EventService } from "../services/event.service";

const router = new Hono<{ Bindings: CloudflareBindings }>();

// GET /api/events - Get user Christian events
router.get(
  "/",
  describeRoute({
    description: "Get user Christian events with optional filtering",
    responses: {
      200: {
        description: "Successfully retrieved events",
        content: {
          "application/json": {
            schema: resolver(EventListResponseSchema)
          }
        }
      },
      400: {
        description: "Bad request - User ID required",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("query", EventQuerySchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const query = c.req.valid("query");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const eventService = new EventService(repositoryFactory);

      const events = await eventService.getAllEvents();

      return c.json({
        success: true,
        data: events,
        pagination: {
          total: events.length,
          limit: query.limit || 10,
          offset: query.offset || 0,
          hasMore: events.length === (query.limit || 10)
        }
      });
    } catch (error: any) {
      console.error("Get events error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

// POST /api/events - Create new Christian event
router.post("/", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const body = await c.req.json();
    const { name, type, date, reminderDays } = body;

    if (!name || !type) {
      return c.json({ error: "Name and type are required" }, 400);
    }

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const eventService = new EventService(repositoryFactory);

    const event = await eventService.addEvent(name, type, date, undefined, reminderDays);

    return c.json({
      success: true,
      data: event
    }, 201);
  } catch (error: any) {
    console.error("Create event error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// GET /api/events/upcoming - Get upcoming Christian events
router.get("/upcoming", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const days = parseInt(c.req.query("days") || "30");

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const eventService = new EventService(repositoryFactory);

    const upcomingEvents = await eventService.getUpcomingEvents(days);

    return c.json({
      success: true,
      data: upcomingEvents,
      count: upcomingEvents.length
    });
  } catch (error: any) {
    console.error("Get upcoming events error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// GET /api/events/:id - Get specific event
router.get("/:id", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    const eventId = c.req.param("id");

    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const eventService = new EventService(repositoryFactory);

    const event = await eventService.getEvent(eventId);

    if (!event) {
      return c.json({ error: "Event not found" }, 404);
    }

    return c.json({
      success: true,
      data: event
    });
  } catch (error: any) {
    console.error("Get event error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// PUT /api/events/:id - Update event
router.put("/:id", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    const eventId = c.req.param("id");

    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const body = await c.req.json();
    const updates = body;

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const eventService = new EventService(repositoryFactory);

    const event = await eventService.updateEvent(eventId, updates);

    return c.json({
      success: true,
      data: event
    });
  } catch (error: any) {
    console.error("Update event error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// DELETE /api/events/:id - Delete event
router.delete("/:id", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    const eventId = c.req.param("id");

    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const eventService = new EventService(repositoryFactory);

    await eventService.deleteEvent(eventId);

    return c.json({
      success: true,
      message: "Event deleted successfully"
    });
  } catch (error: any) {
    console.error("Delete event error:", error);
    return c.json({ error: error.message }, 500);
  }
});

export default router;
