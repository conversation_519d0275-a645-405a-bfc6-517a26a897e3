import * as z from "zod";

// Birthday date schema (nested structure - resolves type conflict)
export const BirthdayDateSchema = z.object({
  day: z.number().min(1).max(31),
  month: z.number().min(1).max(12)
});

// Birthday schema (unified structure as single source of truth)
export const BirthdaySchema = z.object({
  id: z.string().uuid(),
  userId: z.string(),
  name: z.string().min(1).max(100),
  date: BirthdayDateSchema, // ✅ Nested structure (consistent with core types)
  year: z.number().min(1900).max(2100).optional(),
  reminderDays: z.array(z.number().min(0).max(365)).default([1, 7]),
  isActive: z.boolean().default(true), // ✅ Standardized on isActive
  createdAt: z.iso.datetime(),
  updatedAt: z.iso.datetime(),

  // Computed fields (not stored in DB, optional for API responses)
  nextBirthdayDate: z.iso.datetime().optional(),
  daysUntilBirthday: z.number().optional(),
  age: z.number().optional() // Only if year is provided
});

// Create birthday request schema
export const CreateBirthdaySchema = z.object({
  name: z.string().min(1).max(100),
  date: BirthdayDateSchema,
  year: z.number().min(1900).max(2100).optional(),
  notes: z.string().max(500).optional(),
  reminderDays: z.array(z.number().min(0).max(365)).optional()
});

// Update birthday request schema
export const UpdateBirthdaySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  date: BirthdayDateSchema.optional(),
  year: z.number().min(1900).max(2100).optional(),
  notes: z.string().max(500).optional(),
  reminderDays: z.array(z.number().min(0).max(365)).optional(),
  isActive: z.boolean().optional()
});

// Query parameters schema
export const BirthdayQuerySchema = z.object({
  month: z.coerce.number().min(1).max(12).optional(),
  upcoming: z.enum(["true", "false"]).optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
  offset: z.coerce.number().min(0).optional()
});

// Birthday ID parameter schema
export const BirthdayParamsSchema = z.object({
  id: z.string().uuid()
});

// Response schemas
export const BirthdayResponseSchema = z.object({
  success: z.boolean(),
  data: BirthdaySchema,
  message: z.string().optional()
});

export const BirthdayListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(BirthdaySchema),
  pagination: z.object({
    total: z.number(),
    limit: z.number(),
    offset: z.number(),
    hasMore: z.boolean()
  }),
  message: z.string().optional()
});

// Upcoming birthdays response schema
export const UpcomingBirthdaysResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(z.object({
    birthday: BirthdaySchema,
    daysUntil: z.number(),
    nextDate: z.iso.datetime(),
    age: z.number().optional()
  })),
  message: z.string().optional()
});


// Additional Birthday schemas to replace all types from src/types/birthday.ts

export const CreateBirthdayRequestSchema = z.object({
  userId: z.string(),
  name: z.string(),
  day: z.number().min(1).max(31),
  month: z.number().min(1).max(12),
  year: z.number().optional(),
  reminderDays: z.array(z.number()).optional(),
  isActive: z.boolean().optional()
});

export const UpdateBirthdayRequestSchema = z.object({
  name: z.string().optional(),
  day: z.number().min(1).max(31).optional(),
  month: z.number().min(1).max(12).optional(),
  year: z.number().optional(),
  reminderDays: z.array(z.number()).optional(),
  isActive: z.boolean().optional()
});

export const BirthdayStatsSchema = z.object({
  total: z.number(),
  thisMonth: z.number(),
  upcoming30Days: z.number(),
  averageAge: z.number().optional()
});

export const BirthdayNotificationOptionsSchema = z.object({
  reminderDays: z.number(),
  birthdayDate: z.string(),
  personName: z.string()
});

// Birthday reminder types
export const BirthdayReminderTypeSchema = z.enum(["upcoming", "today", "overdue"]);

export const BirthdayReminderSchema = z.object({
  birthday: BirthdaySchema,
  type: BirthdayReminderTypeSchema,
  daysUntil: z.number(),
  message: z.string()
});

// Birthday search and filter options
export const BirthdaySearchOptionsSchema = z.object({
  name: z.string().optional(),
  month: z.number().optional(),
  upcomingDays: z.number().optional(),
  includeInactive: z.boolean().optional(),
  sortBy: z.enum(["name", "date", "age"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional()
});

// Birthday import/export types
export const BirthdayExportSchema = z.object({
  name: z.string(),
  day: z.number(),
  month: z.number(),
  year: z.number().optional(),
  reminderDays: z.array(z.number())
});

export const BirthdayImportResultSchema = z.object({
  imported: z.number(),
  skipped: z.number(),
  errors: z.array(z.string())
});

// Export types (now as single source of truth)
export type Birthday = z.infer<typeof BirthdaySchema>;
export type CreateBirthday = z.infer<typeof CreateBirthdaySchema>;
export type UpdateBirthday = z.infer<typeof UpdateBirthdaySchema>;
export type BirthdayQuery = z.infer<typeof BirthdayQuerySchema>;
export type BirthdayParams = z.infer<typeof BirthdayParamsSchema>;
export type BirthdayResponse = z.infer<typeof BirthdayResponseSchema>;
export type BirthdayListResponse = z.infer<typeof BirthdayListResponseSchema>;
export type UpcomingBirthdaysResponse = z.infer<typeof UpcomingBirthdaysResponseSchema>;

// Additional type exports to replace src/types/birthday.ts
export type CreateBirthdayRequest = z.infer<typeof CreateBirthdayRequestSchema>;
export type UpdateBirthdayRequest = z.infer<typeof UpdateBirthdayRequestSchema>;
export type BirthdayStats = z.infer<typeof BirthdayStatsSchema>;
export type BirthdayNotificationOptions = z.infer<typeof BirthdayNotificationOptionsSchema>;
export type BirthdayReminderType = z.infer<typeof BirthdayReminderTypeSchema>;
export type BirthdayReminder = z.infer<typeof BirthdayReminderSchema>;
export type BirthdaySearchOptions = z.infer<typeof BirthdaySearchOptionsSchema>;
export type BirthdayExport = z.infer<typeof BirthdayExportSchema>;
export type BirthdayImportResult = z.infer<typeof BirthdayImportResultSchema>;
