# 🤖 Novers Telegram Bot Assistant

A comprehensive Telegram bot assistant built on Cloudflare Workers that helps you manage tasks, track birthdays, monitor Christian events, and stay organized with intelligent notifications and AI-powered features.

## ✨ Features

### 📝 Task Management

- **AI-Powered Task Creation**: Convert any message into a well-formatted task using Cloudflare Workers AI
- **Smart Categorization**: Automatic task categorization and priority assignment
- **Deadline Tracking**: Set and monitor task deadlines with intelligent reminders
- **Progress Monitoring**: Track task completion and progress status
- **Rich UI**: Interactive inline keyboards for easy task management

### 🎂 Birthday Tracking

- **Birthday Management**: Add, edit, and track important birthdays
- **Smart Reminders**: Customizable reminder notifications (1, 7, 30 days before)
- **Age Calculation**: Automatic age calculation for birthdays with birth year
- **Upcoming View**: See all upcoming birthdays in the next 30 days
- **Leap Year Support**: Proper handling of leap year birthdays

### ✝️ Christian Events Tracking

- **Fixed Events**: Track events with fixed dates (Christmas, Epiphany, etc.)
- **Calculated Events**: Automatically calculate Easter-based events (Palm Sunday, Good Friday, etc.)
- **Event Notifications**: Get reminders for upcoming Christian holidays
- **Comprehensive Calendar**: Includes major Christian feast days and celebrations
- **Custom Events**: Add your own custom Christian events

### 🔔 Smart Notifications

- **Deadline Alerts**: Automatic notifications for approaching task deadlines
- **Birthday Reminders**: Timely birthday notifications with customizable intervals
- **Event Notifications**: Christian event reminders and spiritual recommendations
- **Notification Management**: View, manage, and customize your notification preferences

### 🔍 Search & Integration

- **Web Search**: Search the web using DuckDuckGo integration
- **Calendar Integration**: Manage and view calendar events
- **Cross-Platform Search**: Search across tasks, birthdays, and events
- **External Tools**: Extensible integration system for third-party services

### 🤖 AI-Powered Features

- **Natural Language Processing**: Convert messages to structured tasks using Llama 3.1 8B
- **Smart Formatting**: Automatic task title, description, and metadata extraction
- **Intelligent Categorization**: AI-powered task categorization and priority assignment
- **Fallback Mechanisms**: Robust error handling with graceful degradation

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and pnpm
- Cloudflare account with Workers and D1 database access
- Telegram Bot Token (from @BotFather)
- Basic knowledge of Cloudflare Workers

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd novers-telegram-bot
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Configure environment variables**
   Create a `.env` file in the root directory:

   ```env
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token
   TELEGRAM_WEBHOOK_SECRET=your_webhook_secret
   ```

4. **Set up Cloudflare resources**

   ```bash
   # Create D1 database
   pnpm dlx wrangler d1 create novers-bot-db

   # Create KV namespace
   pnpm dlx wrangler kv:namespace create "SESSIONS"
   ```

5. **Update wrangler.jsonc**
   Update the database and KV namespace IDs in `wrangler.jsonc` with the values from step 4.

6. **Run database migrations**

   ```bash
   pnpm dlx wrangler d1 execute novers-bot-db --file=./migrations/001_initial_schema.sql
   ```

7. **Deploy to Cloudflare Workers**

   ```bash
   pnpm run deploy
   ```

8. **Set up Telegram webhook** [setwebhook](https://core.telegram.org/bots/api#setwebhook)

   - Using curl

     ```bash
     curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
        -H "Content-Type: application/json" \
        -d '{"url": "https://your-worker.your-subdomain.workers.dev/webhook/telegram", "secret_token": "your_webhook_secret"}'
     ```

   - Using browser

     ```
     https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook?url=<LIVE_URL>/webhook/telegram&secret_token=<YOUR_WEBHOOK_SECRET>
     ```

   For temporal LIVE_URL in local development, use ngrok

   ```
   ngrok http 8787 --domain <your ngrok free domain>
   ```

## 🔧 Configuration

### Environment Variables

| Variable                  | Description                             | Required |
| ------------------------- | --------------------------------------- | -------- |
| `TELEGRAM_BOT_TOKEN`      | Your Telegram bot token from @BotFather | Yes      |
| `TELEGRAM_WEBHOOK_SECRET` | Secret for webhook validation           | Yes      |

### Cloudflare Resources

- **D1 Database**: Stores all user data, tasks, birthdays, events, and notifications
- **KV Namespace**: Manages user sessions and temporary data
- **Workers AI**: Powers the AI task creation and processing features

## 📖 Usage

### Basic Commands

- `/start` - Initialize the bot and show welcome message
- `/help` - Display help information and available commands
- `/tasks` - View your current tasks
- `/birthday` - Access birthday management menu
- `/birthdays` - View upcoming birthdays
- `/events` - View upcoming Christian events
- `/today` - See today's events and birthdays
- `/notifications` - View your notifications
- `/search` - Search across all content
- `/calendar` - Access calendar features

### Creating Tasks

Simply send any message to the bot, and it will be converted into a task using AI:

```
"Buy groceries for dinner party tomorrow"
```

The AI will extract:

- Title: "Buy groceries for dinner party"
- Due date: Tomorrow
- Priority: Medium
- Category: Personal

### Managing Birthdays

1. Use the 🎂 Birthdays menu or `/birthday` command
2. Click "Add Birthday" and follow the prompts
3. Enter name and date (DD/MM or DD/MM/YYYY format)
4. Set reminder preferences (1, 7, 30 days before)

### Tracking Christian Events

The bot automatically tracks major Christian events:

- **Fixed dates**: Christmas, Epiphany, All Saints' Day
- **Calculated dates**: Easter, Palm Sunday, Good Friday, Pentecost
- **Custom events**: Add your own important Christian dates

## 🏗️ Architecture

### Technology Stack

- **Runtime**: Cloudflare Workers
- **Framework**: Hono (lightweight web framework)
- **Bot Framework**: Telegraf
- **Database**: Cloudflare D1 (SQLite)
- **ORM**: Drizzle ORM
- **AI**: Cloudflare Workers AI (Llama 3.1 8B)
- **Language**: TypeScript

### Project Structure

```
src/
├── ai/                 # AI service and prompts
├── bot/               # Telegram bot implementation
├── db/                # Database schema and migrations
├── handlers/          # API route handlers
├── repositories/      # Data access layer
├── services/          # Business logic layer
├── types/            # TypeScript type definitions
└── index.ts          # Main application entry point
```

### Design Patterns

- **Repository Pattern**: Clean data access abstraction
- **Service Layer**: Business logic separation
- **Dependency Injection**: Loose coupling between components
- **Factory Pattern**: Repository and service instantiation

## 🔌 API Documentation

The bot exposes a comprehensive REST API with OpenAPI 3.0 documentation for external integrations.

### Interactive Documentation

- **Swagger UI**: Visit `/docs` on your deployed worker for interactive API documentation
- **OpenAPI Spec**: Access the raw OpenAPI specification at `/openapi.json`
- **Type-Safe**: All endpoints use Zod schemas for request/response validation

### Key Endpoints

#### Task Management

- `GET /api/tasks` - Retrieve user tasks with filtering and pagination
- `POST /api/tasks` - Create new task with AI-powered formatting
- `GET /api/tasks/:id` - Get specific task details
- `PUT /api/tasks/:id` - Update existing task
- `DELETE /api/tasks/:id` - Delete task

#### Birthday Management

- `GET /api/birthdays` - Get user birthdays
- `POST /api/birthdays` - Add new birthday
- `GET /api/birthdays/upcoming` - Get upcoming birthdays

#### Christian Events

- `GET /api/events` - List Christian events
- `GET /api/events/upcoming` - Get upcoming events
- `GET /api/events/today` - Get today's events

#### Notifications

- `GET /api/notifications` - View user notifications
- `POST /api/notifications` - Create custom notification
- `PUT /api/notifications/:id` - Update notification settings

## 🧪 Development

### Local Development

1. **Start development server**

   ```bash
   pnpm run dev
   ```

2. **Run tests**

   ```bash
   pnpm test
   ```

3. **Type checking**
   ```bash
   pnpm run type-check
   ```

### Database Management

```bash
# Apply migrations
pnpm dlx wrangler d1 execute novers-bot-db --file=./migrations/001_initial_schema.sql

# Query database
pnpm dlx wrangler d1 execute novers-bot-db --command="SELECT * FROM tasks LIMIT 5"

# Backup database
pnpm dlx wrangler d1 export novers-bot-db --output=backup.sql
```

## 🚀 Deployment

### Production Deployment

1. **Build the project**

   ```bash
   pnpm run build
   ```

2. **Deploy to Cloudflare Workers**

   ```bash
   pnpm run deploy
   ```

3. **Set up custom domain** (optional)
   ```bash
   pnpm dlx wrangler route add "bot.yourdomain.com/*" your-worker-name
   ```

---

Built with ❤️ using Cloudflare Workers and modern web technologies.
