import { TaskService } from "../services/task.service";
import { RepositoryFactory } from "../repositories";
interface Env {
  TELEGRAM_BOT_TOKEN: string;
  [key: string]: any;
}
import { Telegraf } from "telegraf";

export class NotificationService {
  private taskService: TaskService;
  private botToken: string;
  private bot: Telegraf;

  constructor(env: Env, repositoryFactory: RepositoryFactory) {
    this.taskService = new TaskService(repositoryFactory);
    this.botToken = env.TELEGRAM_BOT_TOKEN;
    this.bot = new Telegraf(this.botToken);
  }

  async sendNotifications(c: any) {
    // Implement logic to check for approaching deadlines and send notifications
    console.log("Sending notifications...");

    const now = new Date();
    const upcomingDeadline = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours from now

    // TODO: Get user ID from context
    const userId = "123"; // Replace with actual user ID

    const tasks = await this.taskService.getUserTasks(userId);

    if (tasks && tasks.length > 0) {
      for (const task of tasks) {
        if (task.dueDate) {
          const message = `Task "${task.title}" is due on ${new Date(task.dueDate).toLocaleDateString()}`;
          try {
            await this.bot.telegram.sendMessage(userId, message);
            console.log("Notification sent:", message);
          } catch (error) {
            console.error("Failed to send notification", error);
          }
        }
      }
    } else {
      console.log("No upcoming tasks found.");
    }

    return c.json({ message: "Notifications sent" });
  }
}
