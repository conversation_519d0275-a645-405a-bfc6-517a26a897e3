import { Context, Next } from "hono";
import { AuthenticationError } from "./error.middleware";
import { getConfig } from "../config";

export async function authMiddleware(c: Context, next: Next) {
  try {
    const config = getConfig(c.env);

    // For webhook endpoint, validate Telegram webhook secret
    if (c.req.path === "/webhook") {
      const secretToken = c.req.header("X-Telegram-Bot-Api-Secret-Token");

      if (!secretToken || secretToken !== config.telegram.webhookSecret) {
        throw new AuthenticationError("Invalid webhook secret token");
      }
    }

    // For API endpoints, you could add additional authentication here
    // For now, we'll rely on Telegram's built-in authentication

    await next();
  } catch (error) {
    if (error instanceof AuthenticationError) {
      throw error;
    }
    throw new AuthenticationError("Authentication failed");
  }
}

// Middleware specifically for API endpoints that require user authentication
export async function apiAuthMiddleware(c: Context, next: Next) {
  try {
    // Extract user information from request
    // This could be from a JWT token, session, or Telegram user data
    const userId = c.req.header("X-User-ID") || c.req.query("userId");

    if (!userId) {
      throw new AuthenticationError("User ID is required");
    }

    // Store user ID in context for use in handlers
    c.set("userId", userId);

    await next();
  } catch (error) {
    if (error instanceof AuthenticationError) {
      throw error;
    }
    throw new AuthenticationError("API authentication failed");
  }
}

// Validate Telegram webhook signature (optional additional security)
export function validateTelegramWebhook(secretToken: string, body: string, signature?: string): boolean {
  if (!signature || !secretToken) {
    return false;
  }

  // Implement HMAC validation if needed
  // For now, we'll use the secret token validation
  return true;
}
