{"name": "novers-telegram-bot", "version": "1.0.0", "description": "A Telegram bot assistant with task management, notifications, and AI capabilities", "type": "module", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "lint:check": "eslint src --ext .ts --max-warnings 0", "db:generate": "drizzle-kit generate", "db:migrate": "wrangler d1 migrations apply BOT_DB --local", "db:migrate:prod": "wrangler d1 migrations apply BOT_DB", "db:studio": "drizzle-kit studio", "prepare": "husky"}, "dependencies": {"@hono/swagger-ui": "^0.5.2", "@hono/zod-openapi": "^1.0.2", "@hono/zod-validator": "^0.7.2", "@scalar/hono-api-reference": "^0.9.13", "drizzle-orm": "^0.44.4", "hono": "^4.8.12", "hono-openapi": "^0.4.8", "telegraf": "^4.16.3", "uuid": "^11.1.0", "zod": "^4.0.14", "zod-openapi": "^5.3.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^24.1.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "drizzle-kit": "^0.31.4", "eslint": "^9.32.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "husky": "^9.1.7", "lint-staged": "^16.1.4", "typescript": "^5.9.2", "wrangler": "^4.27.0"}, "lint-staged": {"*": "pnpm lint:fix"}}