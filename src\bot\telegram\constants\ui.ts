export const ICONS = {
  // Status Icons
  COMPLETED: "✅",
  IN_PROGRESS: "🔄",
  PENDING: "⏳",

  // Priority Icons
  HIGH_PRIORITY: "🔴",
  MEDIUM_PRIORITY: "🟡",
  LOW_PRIORITY: "🟢",

  // Type Icons
  TASK: "📝",
  BIRTHDAY: "🎂",
  EVENT: "✝️",
  NOTIFICATION: "🔔",

  // Action Icons
  SUCCESS: "✅",
  ERROR: "❌",
  WARNING: "⚠️",
  INFO: "ℹ️",

  // Navigation Icons
  BACK: "🔙",
  FORWARD: "➡️",
  UP: "⬆️",
  DOWN: "⬇️",

  // Time Icons
  CLOCK: "🕐",
  CALENDAR: "📅",
  ALARM: "⏰",

  // Other UI Icons
  SEARCH: "🔍",
  SETTINGS: "⚙️",
  HELP: "❓",
  HOME: "🏠"
} as const;

// Session keys for state management
export const SESSION_KEYS = {
  AWAITING_TASK_TITLE: "awaitingTaskTitle",
  AWAITING_TASK_DESCRIPTION: "awaitingTaskDescription",
  AWAITING_BIRTHDAY_NAME: "awaitingBirthdayName",
  AWAITING_BIRTHDAY_DATE: "awaitingBirthdayDate",
  AWAITING_EVENT_NAME: "awaitingEventName",
  AWAITING_INPUT: "awaitingInput",
  SEARCH_MODE: "searchMode",
  TEMP_TASK_DATA: "tempTaskData",
  TEMP_BIRTHDAY_DATA: "tempBirthdayData",
  TEMP_EVENT_DATA: "tempEventData",
  CURRENT_PAGE: "currentPage",
  CURRENT_FILTER: "currentFilter",
  CURRENT_MENU: "currentMenu",
  EDITING_TASK_ID: "editingTaskId",
  EDITING_BIRTHDAY_ID: "editingBirthdayId",
  LAST_ACTION: "lastAction"
} as const;

// Input values and special strings
export const INPUT_VALUES = {
  SEARCH_QUERY: "search_query",
  EVENT_SEARCH_QUERY: "event_search_query",
  SKIP: "skip"
} as const;

// Date and time formats
export const DATE_FORMATS = {
  DD_MM: "DD/MM",
  DD_MM_YYYY: "DD/MM/YYYY",
  DISPLAY_DATE: "DD MMM YYYY",
  SHORT_DATE: "DD/MM",
  FULL_DATE: "dddd, MMMM Do YYYY"
} as const;

// Time-related constants
export const TIME_CONSTANTS = {
  DAYS_IN_ADVANCE: 30,
  REMINDER_DAYS: [1, 7],
  DEFAULT_REMINDER_TIME: "09:00",
  TIMEZONE_DEFAULT: "UTC"
} as const;

// Intent detection keywords
export const INTENT_KEYWORDS = {
  TASK_CREATION: ["todo", "task", "remind me", "need to", "have to", "must", "should"],
  BIRTHDAY: ["birthday", "born", "birth date", "bday"],
  SEARCH: ["search", "find", "look for", "where is"]
} as const;

export type IconType = typeof ICONS[keyof typeof ICONS];
export type SessionKeyType = typeof SESSION_KEYS[keyof typeof SESSION_KEYS];
export type InputValueType = typeof INPUT_VALUES[keyof typeof INPUT_VALUES];
export type DateFormatType = typeof DATE_FORMATS[keyof typeof DATE_FORMATS];
