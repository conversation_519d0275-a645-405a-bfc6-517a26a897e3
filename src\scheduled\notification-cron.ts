// Cloudflare Workers scheduled event handler for notifications
// This file handles the cron trigger for processing notifications

import { NotificationSchedulerService } from "../services/notification-scheduler.service";
import { TelegramBot } from "../bot/telegram";
import { RepositoryFactory } from "../repositories";


export interface ScheduledEvent {
  type: "scheduled";
  scheduledTime: number;
  cron: string;
}


// Process main notification delivery (every 5 minutes)
async function processMainNotifications(
  scheduler: NotificationSchedulerService,
  _ctx: ExecutionContext
): Promise<void> {
  console.log("🔄 Processing main notifications...");

  const stats = await scheduler.processScheduledNotifications();

  console.log("📊 Notification processing stats:", {
    processed: stats.processedNotifications,
    sent: stats.sentNotifications,
    failed: stats.failedNotifications,
    skipped: stats.skippedNotifications,
    processingTime: `${stats.processingTimeMs}ms`
  });

  // Store stats in KV for monitoring (optional)
  // await env.KV.put('notification_stats_latest', JSON.stringify(stats));
}

// Process daily scheduling for birthdays and events (daily at 9 AM)
async function processDailyScheduling(
  scheduler: NotificationSchedulerService,
  repositoryFactory: RepositoryFactory,
  _ctx: ExecutionContext
): Promise<void> {
  console.log("📅 Processing daily scheduling...");

  try {
    // Schedule birthday notifications for upcoming birthdays
    const birthdayRepo = repositoryFactory.getBirthdayRepository();
    const upcomingBirthdays = await birthdayRepo.findUpcoming(30); // Next 30 days

    for (const birthday of upcomingBirthdays) {
      try {
        await scheduler.autoScheduleBirthdayNotifications(birthday.id, birthday.userId);
        console.log(`🎂 Scheduled notifications for birthday: ${birthday.name}`);
      } catch (error) {
        console.error(`❌ Error scheduling birthday notifications for ${birthday.name}:`, error);
      }
    }

    // Schedule Christian event notifications
    const eventRepo = repositoryFactory.getEventRepository();
    const upcomingEvents = await eventRepo.findUpcoming(30); // Next 30 days

    for (const event of upcomingEvents) {
      try {
        // Auto-schedule event notifications for all active users
        const userRepo = repositoryFactory.getUserRepository();
        const activeUsers = await userRepo.findActiveUsers();

        for (const user of activeUsers) {
          // Skip calculated events for now in cron
          if (event.type !== "fixed" || !event.fixedDate) {
            continue;
          }

          // Event notification scheduling would happen here
          console.log(`Would schedule event notification for ${event.name} to user ${user.id}`);
        }
        console.log(`✝️ Scheduled notifications for event: ${event.name}`);
      } catch (error) {
        console.error(`❌ Error scheduling event notifications for ${event.name}:`, error);
      }
    }

    console.log("✅ Daily scheduling completed");
  } catch (error) {
    console.error("❌ Error in daily scheduling:", error);
    throw error;
  }
}

// Process weekly cleanup (Sunday at midnight)
async function processWeeklyCleanup(
  repositoryFactory: RepositoryFactory,
  _ctx: ExecutionContext
): Promise<void> {
  console.log("🧹 Processing weekly cleanup...");

  try {
    const notificationRepo = repositoryFactory.getNotificationRepository();

    // Clean up old sent notifications (older than 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const oldNotifications = await notificationRepo.findOlderThan(thirtyDaysAgo, "sent");

    for (const notification of oldNotifications) {
      await notificationRepo.delete(notification.id);
    }

    console.log(`🗑️ Cleaned up ${oldNotifications.length} old notifications`);

    // Clean up failed notifications older than 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const oldFailedNotifications = await notificationRepo.findOlderThan(sevenDaysAgo, "failed");

    for (const notification of oldFailedNotifications) {
      await notificationRepo.delete(notification.id);
    }

    console.log(`🗑️ Cleaned up ${oldFailedNotifications.length} old failed notifications`);

    console.log("✅ Weekly cleanup completed");
  } catch (error) {
    console.error("❌ Error in weekly cleanup:", error);
    throw error;
  }
}

// Export main scheduled event handler for use in main worker
export default async function handleScheduledEvent(
  event: ScheduledEvent,
  env: Env,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`🕐 Scheduled event triggered at ${new Date(event.scheduledTime).toISOString()}`);
  console.log(`📅 Cron pattern: ${event.cron}`);

  try {
    // Initialize repository factory
    const repositoryFactory = new RepositoryFactory(env.DB);

    // Initialize Telegram bot with mapped env
    const telegramBot = new TelegramBot({
      TELEGRAM_BOT_TOKEN: env.TELEGRAM_BOT_TOKEN,
      TELEGRAM_WEBHOOK_SECRET: env.TELEGRAM_WEBHOOK_SECRET,
      BOT_DB: env.BOT_DB,
      BOT_KV: env.BOT_KV,
      BOT_AI: env.AI,
      AI: env.AI,
      CLOUDFLARE_ACCOUNT_ID: "",
      ENVIRONMENT: "production",
      ASSETS: env.ASSETS
    });

    // Initialize notification scheduler
    const scheduler = new NotificationSchedulerService(
      repositoryFactory,
      telegramBot,
      {
        batchSize: 50,
        maxRetries: 3,
        retryDelayMinutes: 15,
        enableQuietHours: true,
        maxDailyNotificationsPerUser: 10
      }
    );

    // Process notifications based on cron pattern
    switch (event.cron) {
    case "*/5 * * * *": // Every 5 minutes - main notification processing
      await processMainNotifications(scheduler, ctx);
      break;

    case "0 9 * * *": // Daily at 9 AM - birthday and event scheduling
      await processDailyScheduling(scheduler, repositoryFactory, ctx);
      break;

    case "0 0 * * 0": // Weekly on Sunday at midnight - cleanup
      await processWeeklyCleanup(repositoryFactory, ctx);
      break;

    default:
      console.log(`⚠️ Unknown cron pattern: ${event.cron}`);
    }

    console.log("✅ Scheduled event completed successfully");
  } catch (error) {
    console.error("❌ Error in scheduled event:", error);
    throw error;
  }
};
