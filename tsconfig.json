{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "types": ["./worker-configuration.d.ts"], "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@handlers/*": ["src/handlers/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@middleware/*": ["src/middleware/*"], "@ai/*": ["src/ai/*"], "@integrations/*": ["src/integrations/*"], "@schemas/*": ["src/schemas/*"], "@notifications/*": ["src/notifications/*"], "@scheduler/*": ["src/scheduler/*"]}}, "include": ["src/**/*.ts", "tests/**/*.ts", "vitest.config.ts", "docs/type-conversion.ts", "docs/init.ts"], "exclude": ["node_modules/", "dist/"]}