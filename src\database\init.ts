// Database initialization utilities using Drizzle ORM

import { eq } from "drizzle-orm";
import type { ChristianE<PERSON> } from "@/types";
import { DrizzleDB, createDrizzleDB } from "./connection";
import {
  users,
  tasks,
  birthdays,
  christianEvents,
  notifications,
  attachments
} from "./schema";

export class DatabaseInitializer {
  private d1Database: D1Database;
  private db: DrizzleDB;

  constructor(d1Database: D1Database) {
    this.d1Database = d1Database;
    this.db = createDrizzleDB(d1Database);
  }

  // Initialize database with schema and default data
  async initialize(): Promise<void> {
    try {
      console.log("Initializing database...");

      await this.createTables();
      await this.seedChristianEvents();

      console.log("Database initialization completed successfully");
    } catch (error) {
      console.error("Database initialization failed:", error);
      throw error;
    }
  }

  // Create all database tables using Drizzle schema
  private async createTables(): Promise<void> {
    console.log("Creating database tables using Drizzle schema...");

    // Note: In Cloudflare D1, tables are typically created via migrations
    // This method provides a fallback for runtime table creation if needed

    try {
      // The tables are defined in the Drizzle schema and should be created via migrations
      // However, for development/testing purposes, we can create them programmatically

      // Create users table
      await this.d1Database.prepare(`
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          username TEXT,
          firstName TEXT NOT NULL,
          lastName TEXT,
          createdAt TEXT NOT NULL,
          lastActive TEXT NOT NULL,
          preferences TEXT NOT NULL DEFAULT '{"timezone":"UTC","notificationTime":"09:00","mode":"chat"}'
        )
      `).run();

      // Create tasks table
      await this.d1Database.prepare(`
        CREATE TABLE IF NOT EXISTS tasks (
          id TEXT PRIMARY KEY,
          userId TEXT NOT NULL,
          title TEXT NOT NULL,
          description TEXT,
          status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'completed', 'cancelled')),
          priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
          dueDate TEXT,
          reminderDate TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          completedAt TEXT,
          metadata TEXT NOT NULL DEFAULT '{"source":"direct"}',
          attachments TEXT,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
        )
      `).run();

      // Create birthdays table
      await this.d1Database.prepare(`
        CREATE TABLE IF NOT EXISTS birthdays (
          id TEXT PRIMARY KEY,
          userId TEXT NOT NULL,
          name TEXT NOT NULL,
          day INTEGER NOT NULL CHECK (day >= 1 AND day <= 31),
          month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
          year INTEGER,
          reminderDays TEXT NOT NULL DEFAULT '[1,7]',
          suggestions TEXT NOT NULL DEFAULT '[]',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          active INTEGER NOT NULL DEFAULT 1,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
        )
      `).run();

      // Create christian_events table
      await this.d1Database.prepare(`
        CREATE TABLE IF NOT EXISTS christian_events (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL UNIQUE,
          type TEXT NOT NULL CHECK (type IN ('fixed', 'calculated')),
          fixedDate TEXT,
          calculation TEXT,
          reminderDays TEXT NOT NULL DEFAULT '[1,7]',
          recommendations TEXT NOT NULL DEFAULT '[]',
          description TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          active INTEGER NOT NULL DEFAULT 1
        )
      `).run();

      // Create notifications table
      await this.d1Database.prepare(`
        CREATE TABLE IF NOT EXISTS notifications (
          id TEXT PRIMARY KEY,
          userId TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('task-deadline', 'birthday', 'christian-event', 'task-reminder', 'event-reminder', 'custom')),
          entityId TEXT NOT NULL,
          scheduledFor TEXT NOT NULL,
          sentAt TEXT,
          status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled', 'snoozed')),
          message TEXT NOT NULL,
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
          metadata TEXT,
          createdAt TEXT NOT NULL,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
        )
      `).run();

      // Create attachments table
      await this.d1Database.prepare(`
        CREATE TABLE IF NOT EXISTS attachments (
          id TEXT PRIMARY KEY,
          taskId TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('document', 'photo', 'audio', 'video')),
          fileId TEXT NOT NULL,
          fileName TEXT,
          mimeType TEXT,
          fileSize INTEGER,
          uploadedAt TEXT NOT NULL,
          FOREIGN KEY (taskId) REFERENCES tasks(id) ON DELETE CASCADE
        )
      `).run();

      // Create indexes
      await this.createIndexes();

      console.log("Database tables created successfully");
    } catch (error) {
      console.error("Error creating database tables:", error);
      throw error;
    }
  }

  // Create database indexes
  private async createIndexes(): Promise<void> {
    console.log("Creating database indexes...");

    const indexes = [
      "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
      "CREATE INDEX IF NOT EXISTS idx_users_lastActive ON users(lastActive)",
      "CREATE INDEX IF NOT EXISTS idx_tasks_userId ON tasks(userId)",
      "CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)",
      "CREATE INDEX IF NOT EXISTS idx_tasks_dueDate ON tasks(dueDate)",
      "CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority)",
      "CREATE INDEX IF NOT EXISTS idx_tasks_createdAt ON tasks(createdAt)",
      "CREATE INDEX IF NOT EXISTS idx_tasks_user_status ON tasks(userId, status)",
      "CREATE INDEX IF NOT EXISTS idx_birthdays_userId ON birthdays(userId)",
      "CREATE INDEX IF NOT EXISTS idx_birthdays_date ON birthdays(month, day)",
      "CREATE INDEX IF NOT EXISTS idx_birthdays_active ON birthdays(active)",
      "CREATE INDEX IF NOT EXISTS idx_birthdays_user_active ON birthdays(userId, active)",
      "CREATE INDEX IF NOT EXISTS idx_events_type ON christian_events(type)",
      "CREATE INDEX IF NOT EXISTS idx_events_active ON christian_events(active)",
      "CREATE INDEX IF NOT EXISTS idx_events_name ON christian_events(name)",
      "CREATE INDEX IF NOT EXISTS idx_notifications_userId ON notifications(userId)",
      "CREATE INDEX IF NOT EXISTS idx_notifications_scheduledFor ON notifications(scheduledFor)",
      "CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status)",
      "CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type)",
      "CREATE INDEX IF NOT EXISTS idx_notifications_entityId ON notifications(entityId)",
      "CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority)",
      "CREATE INDEX IF NOT EXISTS idx_notifications_pending ON notifications(status, scheduledFor)",
      "CREATE INDEX IF NOT EXISTS idx_attachments_taskId ON attachments(taskId)",
      "CREATE INDEX IF NOT EXISTS idx_attachments_type ON attachments(type)"
    ];

    try {
      for (const index of indexes) {
        await this.d1Database.prepare(index).run();
      }
      console.log("Database indexes created successfully");
    } catch (error) {
      console.error("Error creating database indexes:", error);
      throw error;
    }
  }

  // Seed Christian events data using Drizzle ORM
  private async seedChristianEvents(): Promise<void> {
    console.log("Seeding Christian events...");

    const events: Partial<ChristianEvent>[] = [
      {
        name: "Christmas",
        type: "fixed",
        fixedDate: { month: 12, day: 25 },
        description: "The birth of Jesus Christ",
        reminderDays: [1, 7, 30]
      },
      {
        name: "Easter Sunday",
        type: "calculated",
        calculation: "easter",
        description: "The resurrection of Jesus Christ",
        reminderDays: [1, 7, 14]
      },
      {
        name: "Good Friday",
        type: "calculated",
        calculation: "easter",
        description: "The crucifixion of Jesus Christ",
        reminderDays: [1, 7]
      },
      {
        name: "Palm Sunday",
        type: "calculated",
        calculation: "easter",
        description: "Jesus' triumphal entry into Jerusalem",
        reminderDays: [1, 7]
      },
      {
        name: "Pentecost",
        type: "calculated",
        calculation: "easter",
        description: "The descent of the Holy Spirit",
        reminderDays: [1, 7]
      },
      {
        name: "Epiphany",
        type: "fixed",
        fixedDate: { month: 1, day: 6 },
        description: "The manifestation of Christ to the Gentiles",
        reminderDays: [1, 7]
      }
    ];

    for (const event of events) {
      try {
        if (!event.name) {
          continue;
        }

        // Check if event already exists using Drizzle ORM
        const existing = await this.db
          .select({ id: christianEvents.id })
          .from(christianEvents)
          .where(eq(christianEvents.name, event.name))
          .limit(1);

        if (existing.length === 0) {
          const id = crypto.randomUUID();
          const now = new Date().toISOString();

          // Insert using Drizzle ORM
          await this.db.insert(christianEvents).values({
            id,
            name: event.name,
            type: event.type as "fixed" | "calculated",
            fixedDate: event.fixedDate ? JSON.stringify(event.fixedDate) : null,
            calculation: event.calculation ? JSON.stringify(event.calculation) : null,
            reminderDays: JSON.stringify(event.reminderDays),
            recommendations: "[]",
            description: event.description || null,
            createdAt: now,
            updatedAt: now,
            active: true
          });

          console.log(`Seeded Christian event: ${event.name}`);
        }
      } catch (error) {
        console.error(`Error seeding event ${event.name}:`, error);
      }
    }

    console.log("Christian events seeding completed");
  }

  // Check if database is properly initialized using Drizzle ORM
  async isInitialized(): Promise<boolean> {
    try {
      // Check if tables exist by trying to query them using Drizzle ORM
      await this.db.select().from(users).limit(1);
      await this.db.select().from(tasks).limit(1);
      await this.db.select().from(birthdays).limit(1);
      await this.db.select().from(christianEvents).limit(1);
      await this.db.select().from(notifications).limit(1);
      await this.db.select().from(attachments).limit(1);

      return true;
    } catch (error) {
      console.error("Database is not initialized:", error);
      return false;
    }
  }
}
