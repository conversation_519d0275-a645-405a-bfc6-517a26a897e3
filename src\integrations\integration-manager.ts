/**
 * Integration Manager
 * Coordinates all external integrations and provides unified access
 */

import { SearchIntegration, SearchOptions, SearchResponse } from "./search-integration";
import { CalendarIntegration, CalendarQuery, CalendarEvent, CalendarEventInput } from "./calendar-integration";
import { IntegrationConfig, IntegrationResponse } from "./base-integration";

export interface IntegrationManagerConfig {
  search?: IntegrationConfig;
  calendar?: IntegrationConfig;
  enabledIntegrations?: string[];
}

export interface IntegrationStatus {
  name: string;
  enabled: boolean;
  healthy: boolean;
  lastCheck: Date;
  error?: string;
}

export class IntegrationManager {
  private searchIntegration?: SearchIntegration;
  private calendarIntegration?: CalendarIntegration;
  private config: IntegrationManagerConfig;
  private repositoryFactory: any;

  constructor(config: IntegrationManagerConfig, repositoryFactory?: any) {
    this.config = config;
    this.repositoryFactory = repositoryFactory;
  }

  /**
   * Initialize all enabled integrations
   */
  async initialize(): Promise<void> {
    try {
      const enabledIntegrations = this.config.enabledIntegrations || ["search", "calendar"];

      // Initialize search integration
      if (enabledIntegrations.includes("search") && this.config.search) {
        this.searchIntegration = new SearchIntegration(this.config.search, this.repositoryFactory);
        await this.searchIntegration.initialize();
        console.log("✅ Search integration initialized");
      }

      // Initialize calendar integration
      if (enabledIntegrations.includes("calendar") && this.config.calendar) {
        this.calendarIntegration = new CalendarIntegration(this.config.calendar, this.repositoryFactory);
        await this.calendarIntegration.initialize();
        console.log("✅ Calendar integration initialized");
      }

      console.log("🚀 Integration manager initialized successfully");

    } catch (error) {
      console.error("❌ Failed to initialize integration manager:", error);
      throw error;
    }
  }

  /**
   * Cleanup all integrations
   */
  async cleanup(): Promise<void> {
    try {
      if (this.searchIntegration) {
        await this.searchIntegration.cleanup();
      }

      if (this.calendarIntegration) {
        await this.calendarIntegration.cleanup();
      }

      console.log("🧹 Integration manager cleaned up");

    } catch (error) {
      console.error("❌ Failed to cleanup integration manager:", error);
    }
  }

  /**
   * Get status of all integrations
   */
  async getIntegrationsStatus(): Promise<IntegrationStatus[]> {
    const statuses: IntegrationStatus[] = [];

    // Check search integration
    if (this.searchIntegration) {
      try {
        const status = await this.searchIntegration.getStatus();
        statuses.push({
          name: "search",
          enabled: true,
          healthy: status.success,
          lastCheck: new Date(),
          error: status.error
        });
      } catch (error) {
        statuses.push({
          name: "search",
          enabled: true,
          healthy: false,
          lastCheck: new Date(),
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    } else {
      statuses.push({
        name: "search",
        enabled: false,
        healthy: false,
        lastCheck: new Date(),
        error: "Not configured"
      });
    }

    // Check calendar integration
    if (this.calendarIntegration) {
      try {
        const status = await this.calendarIntegration.getStatus();
        statuses.push({
          name: "calendar",
          enabled: true,
          healthy: status.success,
          lastCheck: new Date(),
          error: status.error
        });
      } catch (error) {
        statuses.push({
          name: "calendar",
          enabled: true,
          healthy: false,
          lastCheck: new Date(),
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    } else {
      statuses.push({
        name: "calendar",
        enabled: false,
        healthy: false,
        lastCheck: new Date(),
        error: "Not configured"
      });
    }

    return statuses;
  }

  // Search Integration Methods

  /**
   * Perform search across all enabled sources
   */
  async search(options: SearchOptions): Promise<IntegrationResponse<SearchResponse>> {
    if (!this.searchIntegration) {
      return {
        success: false,
        error: "Search integration not available"
      };
    }

    return await this.searchIntegration.search(options);
  }

  /**
   * Get search analytics
   */
  async getSearchAnalytics(): Promise<IntegrationResponse<any>> {
    if (!this.searchIntegration) {
      return {
        success: false,
        error: "Search integration not available"
      };
    }

    return await this.searchIntegration.getSearchAnalytics();
  }

  // Calendar Integration Methods

  /**
   * Get calendar events
   */
  async getCalendarEvents(query: CalendarQuery): Promise<IntegrationResponse<CalendarEvent[]>> {
    if (!this.calendarIntegration) {
      return {
        success: false,
        error: "Calendar integration not available"
      };
    }

    return await this.calendarIntegration.getEvents(query);
  }

  /**
   * Create calendar event
   */
  async createCalendarEvent(eventInput: CalendarEventInput): Promise<IntegrationResponse<CalendarEvent>> {
    if (!this.calendarIntegration) {
      return {
        success: false,
        error: "Calendar integration not available"
      };
    }

    return await this.calendarIntegration.createEvent(eventInput);
  }

  /**
   * Update calendar event
   */
  async updateCalendarEvent(eventId: string, updates: Partial<CalendarEventInput>): Promise<IntegrationResponse<CalendarEvent>> {
    if (!this.calendarIntegration) {
      return {
        success: false,
        error: "Calendar integration not available"
      };
    }

    return await this.calendarIntegration.updateEvent(eventId, updates);
  }

  /**
   * Delete calendar event
   */
  async deleteCalendarEvent(eventId: string): Promise<IntegrationResponse<boolean>> {
    if (!this.calendarIntegration) {
      return {
        success: false,
        error: "Calendar integration not available"
      };
    }

    return await this.calendarIntegration.deleteEvent(eventId);
  }

  /**
   * Create calendar event from task
   */
  async createEventFromTask(task: any): Promise<IntegrationResponse<CalendarEvent>> {
    if (!this.calendarIntegration) {
      return {
        success: false,
        error: "Calendar integration not available"
      };
    }

    return await this.calendarIntegration.createEventFromTask(task);
  }

  /**
   * Create calendar event from birthday
   */
  async createEventFromBirthday(birthday: any): Promise<IntegrationResponse<CalendarEvent>> {
    if (!this.calendarIntegration) {
      return {
        success: false,
        error: "Calendar integration not available"
      };
    }

    return await this.calendarIntegration.createEventFromBirthday(birthday);
  }

  /**
   * Get calendar statistics
   */
  async getCalendarStats(): Promise<IntegrationResponse<any>> {
    if (!this.calendarIntegration) {
      return {
        success: false,
        error: "Calendar integration not available"
      };
    }

    return await this.calendarIntegration.getCalendarStats();
  }

  // Utility Methods

  /**
   * Check if integration is available
   */
  isIntegrationAvailable(integration: "search" | "calendar"): boolean {
    switch (integration) {
    case "search":
      return !!this.searchIntegration;
    case "calendar":
      return !!this.calendarIntegration;
    default:
      return false;
    }
  }

  /**
   * Get integration configuration
   */
  getIntegrationConfig(): IntegrationManagerConfig {
    return this.config;
  }

  /**
   * Update integration configuration
   */
  async updateIntegrationConfig(newConfig: Partial<IntegrationManagerConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };

    // Reinitialize if needed
    await this.cleanup();
    await this.initialize();
  }

  /**
   * Test all integrations
   */
  async testIntegrations(): Promise<{ [key: string]: boolean }> {
    const results: { [key: string]: boolean } = {};

    // Test search integration
    if (this.searchIntegration) {
      try {
        const searchResult = await this.search({
          query: "test",
          type: "all",
          limit: 1
        });
        results.search = searchResult.success;
      } catch (error) {
        results.search = false;
      }
    }

    // Test calendar integration
    if (this.calendarIntegration) {
      try {
        const now = new Date();
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

        const calendarResult = await this.getCalendarEvents({
          startDate: now,
          endDate: tomorrow
        });
        results.calendar = calendarResult.success;
      } catch (error) {
        results.calendar = false;
      }
    }

    return results;
  }
}
