// Base repository pattern for database operations using Dr<PERSON>zle ORM

import { DrizzleDB } from "../database/connection";

export abstract class BaseRepository<T> {
  protected db: DrizzleDB;

  constructor(db: DrizzleDB) {
    this.db = db;
  }

  // Utility methods
  protected generateId(): string {
    return crypto.randomUUID();
  }

  protected getCurrentTimestamp(): string {
    return new Date().toISOString();
  }

  // Abstract methods to be implemented by concrete repositories
  protected abstract mapFromDb(row: any): T;
  protected abstract mapToDb(data: Partial<T>): Record<string, any>;
}
