// Notification scheduler service for Cloudflare Workers scheduled events
// Handles automatic notification processing and delivery

import { NotificationService } from "./notification.service";
import { TelegramBot } from "../bot/telegram";
import { RepositoryFactory } from "../repositories";
import type { Notification } from "@/types";

export interface SchedulerStats {
  processedNotifications: number;
  sentNotifications: number;
  failedNotifications: number;
  skippedNotifications: number;
  processingTimeMs: number;
  lastRunTime: Date;
}

export interface SchedulerConfig {
  batchSize: number;
  maxRetries: number;
  retryDelayMinutes: number;
  enableQuietHours: boolean;
  maxDailyNotificationsPerUser: number;
}

export class NotificationSchedulerService {
  private notificationService: NotificationService;
  private telegramBot: TelegramBot;
  private repositoryFactory: RepositoryFactory;
  private config: SchedulerConfig;

  constructor(
    repositoryFactory: RepositoryFactory,
    telegramBot: TelegramBot,
    config?: Partial<SchedulerConfig>
  ) {
    this.repositoryFactory = repositoryFactory;
    this.notificationService = new NotificationService(repositoryFactory);
    this.telegramBot = telegramBot;
    this.config = {
      batchSize: 50,
      maxRetries: 3,
      retryDelayMinutes: 15,
      enableQuietHours: true,
      maxDailyNotificationsPerUser: 10,
      ...config
    };
  }

  // Main scheduled event handler
  async processScheduledNotifications(): Promise<SchedulerStats> {
    const startTime = Date.now();
    console.log("🔄 Starting notification processing...");

    const stats: SchedulerStats = {
      processedNotifications: 0,
      sentNotifications: 0,
      failedNotifications: 0,
      skippedNotifications: 0,
      processingTimeMs: 0,
      lastRunTime: new Date()
    };

    try {
      // Get pending notifications
      const pendingNotifications = await this.notificationService.getPendingNotifications();
      console.log(`📋 Found ${pendingNotifications.length} pending notifications`);

      // Process notifications in batches
      const batches = this.chunkArray(pendingNotifications, this.config.batchSize);

      for (const batch of batches) {
        await this.processBatch(batch, stats);
      }

      stats.processingTimeMs = Date.now() - startTime;
      console.log(`✅ Notification processing completed in ${stats.processingTimeMs}ms`);
      console.log(`📊 Stats: ${stats.sentNotifications} sent, ${stats.failedNotifications} failed, ${stats.skippedNotifications} skipped`);

      return stats;
    } catch (error) {
      console.error("❌ Error processing notifications:", error);
      stats.processingTimeMs = Date.now() - startTime;
      throw error;
    }
  }

  // Process a batch of notifications
  private async processBatch(notifications: Notification[], stats: SchedulerStats): Promise<void> {
    const userNotificationCounts = new Map<string, number>();

    for (const notification of notifications) {
      try {
        stats.processedNotifications++;

        // Check daily notification limit
        const userCount = userNotificationCounts.get(notification.userId) || 0;
        if (userCount >= this.config.maxDailyNotificationsPerUser) {
          console.log(`⏭️ Skipping notification for user ${notification.userId} - daily limit reached`);
          stats.skippedNotifications++;
          continue;
        }

        // Check if notification should be sent now
        if (!this.shouldSendNotification(notification)) {
          stats.skippedNotifications++;
          continue;
        }

        // Send notification
        const success = await this.sendNotification(notification);

        if (success) {
          await this.notificationService.markAsSent(notification.id);
          userNotificationCounts.set(notification.userId, userCount + 1);
          stats.sentNotifications++;
          console.log(`✅ Sent notification ${notification.id} to user ${notification.userId}`);
        } else {
          await this.handleFailedNotification(notification);
          stats.failedNotifications++;
          console.log(`❌ Failed to send notification ${notification.id}`);
        }

        // Small delay between notifications to avoid rate limiting
        await this.delay(100);
      } catch (error) {
        console.error(`❌ Error processing notification ${notification.id}:`, error);
        await this.handleFailedNotification(notification);
        stats.failedNotifications++;
      }
    }
  }

  // Send notification via Telegram
  private async sendNotification(notification: Notification): Promise<boolean> {
    try {
      // Get user's Telegram chat ID (assuming it's the same as userId for now)
      const chatId = notification.userId;

      // Format message with notification type emoji and priority
      const formattedMessage = this.formatNotificationMessage(notification);

      // Send message via Telegram bot
      await this.telegramBot.sendMessage(chatId, formattedMessage, {
        parse_mode: "Markdown",
        disable_notification: notification.priority === "low"
      });

      return true;
    } catch (error) {
      console.error("Failed to send Telegram notification:", error);
      return false;
    }
  }

  // Format notification message for Telegram
  private formatNotificationMessage(notification: Notification): string {
    const priorityEmojis = {
      high: "🔴",
      medium: "🟡",
      low: "🟢"
    };

    const typeEmojis = {
      "task-deadline": "📅",
      "task-reminder": "⏰",
      "birthday": "🎂",
      "christian-event": "✝️",
      "event-reminder": "📅"
    };

    const priorityEmoji = priorityEmojis[notification.priority as keyof typeof priorityEmojis] || "";
    const typeEmoji = typeEmojis[notification.type as keyof typeof typeEmojis] || "📢";

    return `${typeEmoji} ${priorityEmoji} ${notification.message}`;
  }

  // Check if notification should be sent now
  private shouldSendNotification(notification: Notification): boolean {
    const now = new Date();
    const scheduledTime = new Date(notification.scheduledFor);

    // Check if it's time to send
    if (scheduledTime > now) {
      return false;
    }

    // Check quiet hours if enabled
    if (this.config.enableQuietHours) {
      const hour = now.getHours();
      // Default quiet hours: 22:00 - 08:00
      if (hour >= 22 || hour < 8) {
        console.log(`🔇 Skipping notification during quiet hours: ${notification.id}`);
        return false;
      }
    }

    return true;
  }

  // Handle failed notification
  private async handleFailedNotification(notification: Notification): Promise<void> {
    const metadata = notification.metadata;
    const attempts = (metadata?.deliveryAttempts || 0) + 1;

    if (attempts >= this.config.maxRetries) {
      // Mark as permanently failed
      await this.notificationService.markAsFailed(notification.id);
      console.log(`💀 Notification ${notification.id} permanently failed after ${attempts} attempts`);
    } else {
      // Schedule retry
      const retryTime = new Date();
      retryTime.setMinutes(retryTime.getMinutes() + this.config.retryDelayMinutes);

      const notificationRepo = this.repositoryFactory.getNotificationRepository();
      await notificationRepo.update(notification.id, {
        scheduledFor: retryTime.toISOString(),
        metadata: {
          ...metadata,
          snoozeCount: metadata?.snoozeCount ?? 0,
          retryCount: (metadata?.retryCount || 0) + 1,
          deliveryAttempts: attempts,
          lastFailureTime: new Date().toISOString()
        }
      });

      console.log(`🔄 Scheduled retry for notification ${notification.id} at ${retryTime.toISOString()}`);
    }
  }

  // Auto-schedule notifications for new tasks
  async autoScheduleTaskNotifications(taskId: string, userId: string): Promise<void> {
    const taskRepo = this.repositoryFactory.getTaskRepository();
    const task = await taskRepo.findById(taskId);

    if (!task || !task.dueDate) {
      return;
    }

    console.log(`📅 Auto-scheduling notifications for task: ${task.title}`);
    await this.notificationService.scheduleTaskReminder(taskId, userId);
  }

  // Auto-schedule notifications for birthdays
  async autoScheduleBirthdayNotifications(birthdayId: string, userId: string): Promise<void> {
    const birthdayRepo = this.repositoryFactory.getBirthdayRepository();
    const birthday = await birthdayRepo.findById(birthdayId);

    if (!birthday) {
      return;
    }

    console.log(`🎂 Auto-scheduling notifications for birthday: ${birthday.name}`);

    // Create birthday date for current year
    const currentYear = new Date().getFullYear();
    const birthdayDate = new Date(currentYear, birthday.date.month - 1, birthday.date.day);

    // If birthday has already passed this year, schedule for next year
    if (birthdayDate < new Date()) {
      birthdayDate.setFullYear(currentYear + 1);
    }

    await this.notificationService.scheduleBirthdayReminder(
      birthdayId,
      userId,
      birthday.name,
      birthdayDate
    );
  }

  // Get notification statistics
  async getNotificationStats(): Promise<{
    totalPending: number;
    totalSent: number;
    totalFailed: number;
    recentActivity: Notification[];
  }> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();

    const [pending, sent, failed, recent] = await Promise.all([
      notificationRepo.findByStatus("pending"),
      notificationRepo.findByStatus("sent"),
      notificationRepo.findByStatus("failed"),
      notificationRepo.findByStatus("sent") // Get recent sent notifications
    ]);

    return {
      totalPending: pending.length,
      totalSent: sent.length,
      totalFailed: failed.length,
      recentActivity: recent.slice(0, 10) // Last 10 notifications
    };
  }

  // Utility methods
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
