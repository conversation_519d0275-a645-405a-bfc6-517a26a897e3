/**
 * Integration Service
 * Provides high-level integration functionality for the application
 */

import { IntegrationManager, IntegrationManagerConfig } from "../integrations/integration-manager";
import { SearchOptions, SearchResponse } from "../integrations/search-integration";
import { CalendarQuery, CalendarEvent, CalendarEventInput } from "../integrations/calendar-integration";
import { RepositoryFactory } from "../repositories";

export class IntegrationService {
  private integrationManager: IntegrationManager;
  private repositoryFactory: RepositoryFactory;

  constructor(repositoryFactory: RepositoryFactory, config?: IntegrationManagerConfig) {
    this.repositoryFactory = repositoryFactory;

    // Default configuration
    const defaultConfig: IntegrationManagerConfig = {
      search: {
        baseUrl: "https://api.duckduckgo.com",
        timeout: 10000,
        retryAttempts: 2,
        rateLimitPerMinute: 30
      },
      calendar: {
        baseUrl: "internal",
        timeout: 5000,
        retryAttempts: 1
      },
      enabledIntegrations: ["search", "calendar"]
    };

    this.integrationManager = new IntegrationManager(
      { ...defaultConfig, ...config },
      repositoryFactory
    );
  }

  /**
   * Initialize integration service
   */
  async initialize(): Promise<void> {
    await this.integrationManager.initialize();
  }

  /**
   * Cleanup integration service
   */
  async cleanup(): Promise<void> {
    await this.integrationManager.cleanup();
  }

  // Search Methods

  /**
   * Search for content across all sources
   */
  async searchAll(query: string, userId: string, limit: number = 10): Promise<SearchResponse | null> {
    try {
      const searchOptions: SearchOptions = {
        query,
        type: "all",
        limit,
        safeSearch: true
      };

      const result = await this.integrationManager.search(searchOptions);

      if (result.success && result.data) {
        // Log search for analytics
        await this.logUserSearch(userId, query, result.data.results.length);
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("Search failed:", error);
      return null;
    }
  }

  /**
   * Search tasks only
   */
  async searchTasks(query: string, userId: string): Promise<SearchResponse | null> {
    try {
      const searchOptions: SearchOptions = {
        query,
        type: "tasks",
        limit: 20
      };

      const result = await this.integrationManager.search(searchOptions);

      if (result.success && result.data) {
        await this.logUserSearch(userId, query, result.data.results.length, "tasks");
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("Task search failed:", error);
      return null;
    }
  }

  /**
   * Search web content only
   */
  async searchWeb(query: string, userId: string): Promise<SearchResponse | null> {
    try {
      const searchOptions: SearchOptions = {
        query,
        type: "web",
        limit: 10,
        safeSearch: true
      };

      const result = await this.integrationManager.search(searchOptions);

      if (result.success && result.data) {
        await this.logUserSearch(userId, query, result.data.results.length, "web");
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("Web search failed:", error);
      return null;
    }
  }

  // Calendar Methods

  /**
   * Get upcoming events for user
   */
  async getUpcomingEvents(_userId: string, days: number = 7): Promise<CalendarEvent[]> {
    try {
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + days);

      const query: CalendarQuery = {
        startDate,
        endDate,
        includeAllDay: true
      };

      const result = await this.integrationManager.getCalendarEvents(query);

      if (result.success && result.data) {
        return result.data;
      }

      return [];
    } catch (error) {
      console.error("Failed to get upcoming events:", error);
      return [];
    }
  }

  /**
   * Create calendar event from task
   */
  async scheduleTaskDeadline(taskId: string, userId: string): Promise<CalendarEvent | null> {
    try {
      const taskRepo = this.repositoryFactory.getTaskRepository();
      const task = await taskRepo.findById(taskId);

      if (!task || !task.dueDate) {
        return null;
      }

      const result = await this.integrationManager.createEventFromTask(task);

      if (result.success && result.data) {
        // Log calendar action
        await this.logCalendarAction(userId, "create_task_event", taskId);
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("Failed to schedule task deadline:", error);
      return null;
    }
  }

  /**
   * Create calendar event from birthday
   */
  async scheduleBirthdayReminder(birthdayId: string, userId: string): Promise<CalendarEvent | null> {
    try {
      const birthdayRepo = this.repositoryFactory.getBirthdayRepository();
      const birthday = await birthdayRepo.findById(birthdayId);

      if (!birthday) {
        return null;
      }

      const result = await this.integrationManager.createEventFromBirthday(birthday);

      if (result.success && result.data) {
        await this.logCalendarAction(userId, "create_birthday_event", birthdayId);
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("Failed to schedule birthday reminder:", error);
      return null;
    }
  }

  /**
   * Create custom calendar event
   */
  async createCustomEvent(eventInput: CalendarEventInput, userId: string): Promise<CalendarEvent | null> {
    try {
      const result = await this.integrationManager.createCalendarEvent(eventInput);

      if (result.success && result.data) {
        await this.logCalendarAction(userId, "create_custom_event", result.data.id);
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("Failed to create custom event:", error);
      return null;
    }
  }

  /**
   * Get calendar overview for user
   */
  async getCalendarOverview(userId: string): Promise<any> {
    try {
      const statsResult = await this.integrationManager.getCalendarStats();
      const upcomingEvents = await this.getUpcomingEvents(userId, 30);

      const overview = {
        stats: statsResult.success ? statsResult.data : null,
        upcomingEvents: upcomingEvents.slice(0, 5), // Next 5 events
        todayEvents: upcomingEvents.filter(event => {
          const today = new Date();
          const eventDate = new Date(event.startTime);
          return eventDate.toDateString() === today.toDateString();
        }),
        thisWeekEvents: upcomingEvents.filter(event => {
          const weekFromNow = new Date();
          weekFromNow.setDate(weekFromNow.getDate() + 7);
          return event.startTime <= weekFromNow;
        })
      };

      return overview;
    } catch (error) {
      console.error("Failed to get calendar overview:", error);
      return null;
    }
  }

  // Integration Status and Management

  /**
   * Get integration status
   */
  async getIntegrationStatus(): Promise<any> {
    try {
      const statuses = await this.integrationManager.getIntegrationsStatus();
      const testResults = await this.integrationManager.testIntegrations();

      return {
        integrations: statuses,
        tests: testResults,
        lastChecked: new Date()
      };
    } catch (error) {
      console.error("Failed to get integration status:", error);
      return null;
    }
  }

  /**
   * Test integration functionality
   */
  async testIntegrations(): Promise<{ [key: string]: boolean }> {
    try {
      return await this.integrationManager.testIntegrations();
    } catch (error) {
      console.error("Integration tests failed:", error);
      return {};
    }
  }

  // Analytics and Logging

  /**
   * Log user search for analytics
   */
  private async logUserSearch(userId: string, query: string, resultCount: number, type: string = "all"): Promise<void> {
    try {
      // In a real implementation, this would log to analytics service
      console.log(`Search logged: User ${userId} searched "${query}" (${type}) - ${resultCount} results`);
    } catch (error) {
      console.error("Failed to log search:", error);
    }
  }

  /**
   * Log calendar action for analytics
   */
  private async logCalendarAction(userId: string, action: string, entityId: string): Promise<void> {
    try {
      // In a real implementation, this would log to analytics service
      console.log(`Calendar action logged: User ${userId} performed ${action} on ${entityId}`);
    } catch (error) {
      console.error("Failed to log calendar action:", error);
    }
  }

  /**
   * Get user integration usage statistics
   */
  async getUserIntegrationStats(_userId: string): Promise<any> {
    // try {
    // Placeholder for user-specific integration statistics
    return {
      searchCount: 0,
      calendarEventsCreated: 0,
      lastSearchDate: null,
      lastCalendarAction: null,
      favoriteSearchTypes: [],
      mostUsedIntegrations: []
    };
    // } catch (error) {
    //   console.error("Failed to get user integration stats:", error);
    //   return null;
    // }
  }

  // Utility Methods

  /**
   * Check if integration is available
   */
  isIntegrationAvailable(integration: "search" | "calendar"): boolean {
    return this.integrationManager.isIntegrationAvailable(integration);
  }

  /**
   * Format search results for display
   */
  formatSearchResults(searchResponse: SearchResponse): string {
    if (!searchResponse.results || searchResponse.results.length === 0) {
      return "🔍 No results found for your search.";
    }

    let formatted = `🔍 *Search Results* (${searchResponse.results.length} found)\n\n`;

    searchResponse.results.slice(0, 5).forEach((result, _index) => {
      const emoji = this.getResultEmoji(result.type);
      formatted += `${emoji} *${result.title}*\n`;
      formatted += `${result.description.substring(0, 100)}${result.description.length > 100 ? "..." : ""}\n`;

      if (result.url) {
        formatted += `🔗 ${result.url}\n`;
      }

      formatted += `📊 Score: ${Math.round(result.relevanceScore * 100)}%\n\n`;
    });

    if (searchResponse.suggestions && searchResponse.suggestions.length > 0) {
      formatted += `💡 *Suggestions:* ${searchResponse.suggestions.join(", ")}`;
    }

    return formatted;
  }

  /**
   * Get emoji for result type
   */
  private getResultEmoji(type: string): string {
    switch (type) {
    case "task": return "📝";
    case "web": return "🌐";
    case "knowledge": return "📚";
    case "document": return "📄";
    default: return "🔍";
    }
  }
}
