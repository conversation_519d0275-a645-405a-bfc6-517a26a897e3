# TypeScript Fixes Required for Production Telegraf Bot

## Critical Issues Identified

### 1. **Session Type Compatibility Issues**
The main problem is that `ProductionSessionData` extends `Scenes.WizardSessionData` but Telegraf's type system expects the session to be wrapped in `WizardSession<T>`. This creates a type mismatch.

**Current Issue:**
```typescript
// This doesn't work with Telegraf's type system
export interface ProductionSessionData extends Scenes.WizardSessionData {
  user?: { ... };
  rateLimit?: RateLimitData;
}

export type ProductionBotContext = Scenes.WizardContext<ProductionSessionData>;
```

**Solution Required:**
```typescript
// Simplified session data that works with Telegraf
export interface ProductionSessionData extends Scenes.WizardSessionData {
  user?: { ... };
  rateLimit?: RateLimitData;
}

// Use standard Telegraf context
export type ProductionBotContext = Scenes.WizardContext<ProductionSessionData>;
```

### 2. **Wizard Scene Return Type Issues**
All wizard step methods are returning `ctx.wizard.next()` which returns a `WizardContextWizard` object, but the methods are typed to return `void`.

**Fix Required:**
```typescript
// Change from:
private async stepTitle(ctx: ProductionBotContext): Promise<void> {
  // ...
  return ctx.wizard.next(); // ❌ Type error
}

// To:
private async stepTitle(ctx: ProductionBotContext): Promise<any> {
  // ...
  return ctx.wizard.next(); // ✅ Works
}
```

### 3. **Wizard State Assignment Issues**
The wizard state is read-only and cannot be assigned directly.

**Fix Required:**
```typescript
// Change from:
ctx.wizard.state = { taskData: {} } as TaskWizardSession; // ❌ Read-only

// To:
Object.assign(ctx.wizard.state, { taskData: {} }); // ✅ Works
```

### 4. **Missing Import Paths**
Several imports are using `@/` aliases that don't resolve correctly.

**Imports to Fix:**
- `@/repositories` → `../../../repositories`
- `@/ai/ai.service` → `../../../ai/ai.service`
- `@/services/*` → `../../../services/*`
- `Env` type needs to be imported from `../../../types/env`

### 5. **Session Property Access Issues**
The session properties are not accessible because of the type wrapper.

**Fix Required:**
```typescript
// Change from:
ctx.session?.user?.id // ❌ Property 'user' does not exist

// To:
(ctx.session as any)?.user?.id // ✅ Works with type assertion
// Or better: create proper type guards
```

## Recommended Implementation Strategy

### Phase 1: Simplify Session Types
1. Remove complex session inheritance
2. Use type assertions where needed
3. Focus on functionality over perfect typing

### Phase 2: Fix Wizard Scenes
1. Update return types to `Promise<any>`
2. Fix state assignment using `Object.assign`
3. Add proper error handling

### Phase 3: Fix Import Paths
1. Replace all `@/` imports with relative paths
2. Add missing type imports
3. Ensure all dependencies are available

### Phase 4: Test and Validate
1. Run TypeScript compilation
2. Test wizard flows
3. Verify session persistence

## Quick Fix Implementation

The fastest way to get this working is to:

1. **Simplify the context type:**
```typescript
export type ProductionBotContext = Scenes.WizardContext & {
  session: any; // Temporary fix
};
```

2. **Update wizard methods:**
```typescript
private async stepTitle(ctx: ProductionBotContext): Promise<any> {
  // Use Object.assign for state
  Object.assign(ctx.wizard.state, { taskData: {} });
  
  // Return wizard navigation
  return ctx.wizard.next();
}
```

3. **Fix imports:**
```typescript
import { RepositoryFactory } from "../../../repositories";
import { Env } from "../../../types/env";
```

4. **Use type assertions for session access:**
```typescript
const session = ctx.session as ProductionSessionData;
if (session?.user?.id) {
  // Access user data
}
```

This approach prioritizes getting the bot functional while maintaining the advanced features. The type safety can be improved incrementally once the core functionality is working.

## Files That Need Immediate Attention

1. `src/bot/telegram/core/production-types.ts` - Session type definitions
2. `src/bot/telegram/core/production-core.ts` - Import paths and context usage
3. `src/bot/telegram/core/production-session.ts` - Session management
4. `src/bot/telegram/scenes/*.ts` - All wizard scenes
5. `src/bot/telegram/production-bot.ts` - Main bot integration

## Expected Outcome

After implementing these fixes:
- ✅ TypeScript compilation will succeed
- ✅ Wizard scenes will work correctly
- ✅ Session management will persist data
- ✅ RBAC and rate limiting will function
- ✅ All advanced Telegraf patterns will be operational

The production-scale bot will be fully functional with proper error handling, user management, and monetization features.
