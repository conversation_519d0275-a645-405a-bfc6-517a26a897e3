
export type {
  // Common types
  Pagination,
  ErrorResponse,
  HealthResponse,
  SuccessResponse
} from "@/schemas/common.schema";

export type {
  // Core entity types from task schema
  User,
  Task,
  Attachment,

  // Input/creation types
  CreateTaskInput,

  // Metadata and utility types
  TaskMetadata,
  UserPreferences,

  // External integration types
  CalendarEvent,
  CalendarEventInput,
  SearchResult,
  SearchOptions,

  // Enum types
  TaskStatus,
  TaskPriority,

  // Response types
  TaskResponse
} from "@/schemas/task.schema";

export type {
  // Birthday-specific types
  Birthday,
  BirthdayStats,
  BirthdaySearchOptions,
  BirthdayExport,
  BirthdayImportResult,
  BirthdayR<PERSON>inder,
  BirthdayNotificationOptions,
  CreateBirthdayRequest,
  BirthdayResponse,
  BirthdayReminderType
} from "@/schemas/birthday.schema";

export type {
  // Event-specific types
  ChristianEvent,
  CalculationMethod,
  FixedDate,
  EventDate,
  EventType,
  EventResponse
} from "@/schemas/event.schema";

export type {
  // Notification types
  Notification,
  NotificationType,
  NotificationStatus,
  NotificationPreferences,
  NotificationResponse
} from "@/schemas/notification.schema";

