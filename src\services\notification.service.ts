// Enhanced notification service for managing notifications and reminders

import { RepositoryFactory } from "../repositories";
import type {
  Notification,
  Task,
  User
} from "@/types";

export interface NotificationPreferences {
  enabled: boolean;
  timezone: string;
  quietHours: {
    start: string; // HH:MM format
    end: string;   // HH:MM format
  };
  intervals: {
    taskDeadline: number[]; // Hours before deadline [24, 1]
    birthday: number[];     // Days before birthday [7, 1]
    christianEvent: number[]; // Days before event [3, 1]
  };
  maxDailyNotifications: number;
  snoozeMinutes: number;
}

export interface NotificationScheduleOptions {
  intervals?: number[]; // Custom intervals in hours/days
  respectQuietHours?: boolean;
  priority?: "low" | "medium" | "high";
  recurring?: boolean;
  customMessage?: string;
}

export interface NotificationStats {
  totalScheduled: number;
  totalSent: number;
  totalFailed: number;
  successRate: number;
  averageDeliveryTime: number;
  userEngagement: number;
}

export class NotificationService {
  private repositoryFactory: RepositoryFactory;
  private defaultPreferences: NotificationPreferences = {
    enabled: true,
    timezone: "UTC",
    quietHours: {
      start: "22:00",
      end: "08:00"
    },
    intervals: {
      taskDeadline: [24, 1], // 1 day and 1 hour before
      birthday: [7, 1],      // 1 week and 1 day before
      christianEvent: [3, 1] // 3 days and 1 day before
    },
    maxDailyNotifications: 10,
    snoozeMinutes: 60
  };

  constructor(repositoryFactory: RepositoryFactory) {
    this.repositoryFactory = repositoryFactory;
  }

  // Create a notification with enhanced options
  async createNotification(
    userId: string,
    type: Notification["type"],
    entityId: string,
    scheduledFor: Date,
    message: string,
    _options?: NotificationScheduleOptions
  ): Promise<Notification> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();

    // Check user preferences and quiet hours
    const userPrefs = await this.getUserPreferences(userId);
    const adjustedSchedule = this.adjustForQuietHours(scheduledFor, userPrefs);

    return notificationRepo.create({
      id: this.generateId(),
      userId,
      type,
      entityId,
      scheduledFor: adjustedSchedule.toISOString(),
      status: "pending",
      message,
      createdAt: new Date().toISOString()
    });
  }

  // Get pending notifications that should be sent
  async getPendingNotifications(): Promise<Notification[]> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();
    return notificationRepo.findPendingNotifications();
  }

  // Mark notification as sent
  async markAsSent(notificationId: string): Promise<void> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();
    await notificationRepo.updateStatus(notificationId, "sent");
  }

  // Mark notification as failed
  async markAsFailed(notificationId: string): Promise<void> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();
    await notificationRepo.updateStatus(notificationId, "failed");
  }

  // Schedule task reminder with smart intervals
  async scheduleTaskReminder(taskId: string, userId: string, reminderDate?: Date): Promise<void> {
    const taskRepo = this.repositoryFactory.getTaskRepository();
    const task = await taskRepo.findById(taskId);

    if (!task) {
      throw new Error("Task not found");
    }

    const userPrefs = await this.getUserPreferences(userId);
    const intervals = userPrefs.intervals.taskDeadline;

    // If no specific reminder date, use task due date with intervals
    if (!reminderDate && task.dueDate) {
      for (const hours of intervals) {
        const scheduleTime = new Date(task.dueDate);
        scheduleTime.setHours(scheduleTime.getHours() - hours);

        if (scheduleTime > new Date()) {
          const message = this.generateTaskReminderMessage(task, hours);
          await this.createNotification(
            userId,
            "task-reminder",
            taskId,
            scheduleTime,
            message,
            { priority: task.priority as "low" | "medium" | "high" }
          );
        }
      }
    } else if (reminderDate) {
      const message = this.generateTaskReminderMessage(task, 0);
      await this.createNotification(
        userId,
        "task-reminder",
        taskId,
        reminderDate,
        message
      );
    }
  }

  // Schedule birthday reminder with smart intervals
  async scheduleBirthdayReminder(
    birthdayId: string,
    userId: string,
    personName: string,
    birthdayDate: Date
  ): Promise<void> {
    const userPrefs = await this.getUserPreferences(userId);
    const intervals = userPrefs.intervals.birthday;

    for (const days of intervals) {
      const scheduleTime = new Date(birthdayDate);
      scheduleTime.setDate(scheduleTime.getDate() - days);

      if (scheduleTime > new Date()) {
        const message = this.generateBirthdayReminderMessage(personName, days);
        await this.createNotification(
          userId,
          "birthday",
          birthdayId,
          scheduleTime,
          message,
          { priority: "medium" }
        );
      }
    }
  }

  // Schedule Christian event reminder with smart intervals
  async scheduleEventReminder(
    eventId: string,
    userId: string,
    eventName: string,
    eventDate: Date
  ): Promise<void> {
    const userPrefs = await this.getUserPreferences(userId);
    const intervals = userPrefs.intervals.christianEvent;

    for (const days of intervals) {
      const scheduleTime = new Date(eventDate);
      scheduleTime.setDate(scheduleTime.getDate() - days);

      if (scheduleTime > new Date()) {
        const message = this.generateEventReminderMessage(eventName, days);
        await this.createNotification(
          userId,
          "event-reminder",
          eventId,
          scheduleTime,
          message,
          { priority: "medium" }
        );
      }
    }
  }

  // Get user notifications
  async getUserNotifications(
    userId: string,
    status?: Notification["status"],
    limit: number = 50
  ): Promise<Notification[]> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();
    return notificationRepo.findByUserId(userId, status, limit);
  }


  // Update notification
  async updateNotification(
    notificationId: string,
    userId: string,
    updates: Partial<Notification>
  ): Promise<Notification | null> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();

    // First check if the notification belongs to the user
    const notification = await notificationRepo.findById(notificationId);
    if (!notification || notification.userId !== userId) {
      return null;
    }

    return notificationRepo.update(notificationId, updates);
  }

  // Delete notification
  async deleteNotification(notificationId: string, userId: string): Promise<boolean> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();

    // First check if the notification belongs to the user
    const notification = await notificationRepo.findById(notificationId);
    if (!notification || notification.userId !== userId) {
      return false;
    }

    return notificationRepo.delete(notificationId);
  }

  // Cancel notification
  async cancelNotification(notificationId: string): Promise<void> {
    const notificationRepo = this.repositoryFactory.getNotificationRepository();
    await notificationRepo.updateStatus(notificationId, "cancelled");
  }

  // Process and send pending notifications
  async processPendingNotifications(): Promise<{
    sent: number;
    failed: number;
  }> {
    const pendingNotifications = await this.getPendingNotifications();
    let sent = 0;
    let failed = 0;

    for (const notification of pendingNotifications) {
      try {
        // Here you would integrate with Telegram API to send the notification
        // For now, we'll just mark it as sent
        console.log(`Sending notification to user ${notification.userId}: ${notification.message}`);

        await this.markAsSent(notification.id);
        sent++;
      } catch (error) {
        console.error(`Failed to send notification ${notification.id}:`, error);
        await this.markAsFailed(notification.id);
        failed++;
      }
    }

    return { sent, failed };
  }

  // Get user notification preferences
  async getUserPreferences(userId: string): Promise<NotificationPreferences> {
    const userRepo = this.repositoryFactory.getUserRepository();
    const user = await userRepo.findById(userId);

    if (!user || !user.preferences) {
      return this.defaultPreferences;
    }

    // Merge user preferences with defaults
    return {
      ...this.defaultPreferences,
      timezone: user.preferences.timezone || this.defaultPreferences.timezone
      // Add other preference mappings as needed
    };
  }

  // Update user notification preferences
  async updateUserPreferences(userId: string, preferences: Partial<NotificationPreferences>): Promise<void> {
    const userRepo = this.repositoryFactory.getUserRepository();
    const user = await userRepo.findById(userId);

    if (!user) {
      throw new Error("User not found");
    }

    // Map notification preferences to user preferences format
    const userPrefsUpdate: Partial<User["preferences"]> = {};

    if (preferences.timezone) {
      userPrefsUpdate.timezone = preferences.timezone;
    }

    if (preferences.enabled !== undefined) {
      userPrefsUpdate.notificationTime = preferences.enabled ? "09:00" : "disabled";
    }

    await userRepo.updatePreferences(userId, userPrefsUpdate);
  }

  // Adjust notification time for quiet hours
  private adjustForQuietHours(scheduledTime: Date, preferences: NotificationPreferences): Date {
    if (!preferences.quietHours) {
      return scheduledTime;
    }

    const adjusted = new Date(scheduledTime);
    const timeStr = adjusted.toTimeString().substring(0, 5); // HH:MM format

    const quietStart = preferences.quietHours.start;
    const quietEnd = preferences.quietHours.end;

    // Check if scheduled time falls within quiet hours
    if (this.isTimeInQuietHours(timeStr, quietStart, quietEnd)) {
      // Move to end of quiet hours
      const [endHour, endMinute] = quietEnd.split(":").map(Number);
      adjusted.setHours(endHour, endMinute, 0, 0);

      // If that's in the past, move to next day
      if (adjusted <= new Date()) {
        adjusted.setDate(adjusted.getDate() + 1);
      }
    }

    return adjusted;
  }

  // Check if time is within quiet hours
  private isTimeInQuietHours(time: string, quietStart: string, quietEnd: string): boolean {
    const timeMinutes = this.timeToMinutes(time);
    const startMinutes = this.timeToMinutes(quietStart);
    const endMinutes = this.timeToMinutes(quietEnd);

    if (startMinutes <= endMinutes) {
      // Same day quiet hours (e.g., 22:00 to 08:00 next day)
      return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
    } else {
      // Overnight quiet hours (e.g., 22:00 to 08:00 next day)
      return timeMinutes >= startMinutes || timeMinutes <= endMinutes;
    }
  }

  // Convert time string to minutes since midnight
  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  }

  // Generate task reminder message
  private generateTaskReminderMessage(task: Task, hoursBeforeDeadline: number): string {
    const priorityEmoji = {
      high: "🔴",
      medium: "🟡",
      low: "🟢"
    };

    const emoji = priorityEmoji[task.priority as keyof typeof priorityEmoji] || "📝";

    if (hoursBeforeDeadline === 0) {
      return `${emoji} Task Due Now: "${task.title}"`;
    } else if (hoursBeforeDeadline === 1) {
      return `${emoji} Task Due in 1 Hour: "${task.title}"`;
    } else if (hoursBeforeDeadline < 24) {
      return `${emoji} Task Due in ${hoursBeforeDeadline} Hours: "${task.title}"`;
    } else {
      const days = Math.floor(hoursBeforeDeadline / 24);
      return `${emoji} Task Due in ${days} Day${days > 1 ? "s" : ""}: "${task.title}"`;
    }
  }

  // Generate birthday reminder message
  private generateBirthdayReminderMessage(personName: string, daysBeforeBirthday: number): string {
    if (daysBeforeBirthday === 0) {
      return `🎂 Today is ${personName}'s Birthday! 🎉`;
    } else if (daysBeforeBirthday === 1) {
      return `🎂 Tomorrow is ${personName}'s Birthday! 🎉`;
    } else {
      return `🎂 ${personName}'s Birthday is in ${daysBeforeBirthday} days! 🎉`;
    }
  }

  // Generate event reminder message
  private generateEventReminderMessage(eventName: string, daysBeforeEvent: number): string {
    if (daysBeforeEvent === 0) {
      return `✝️ Today is ${eventName}! 🙏`;
    } else if (daysBeforeEvent === 1) {
      return `✝️ Tomorrow is ${eventName}! 🙏`;
    } else {
      return `✝️ ${eventName} is in ${daysBeforeEvent} days! 🙏`;
    }
  }

  private generateId(): string {
    return crypto.randomUUID();
  }
}
