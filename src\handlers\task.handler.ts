import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator as z<PERSON><PERSON><PERSON>tor } from "hono-openapi/zod";
import { TaskService } from "../services/task.service";
import { AIService } from "../ai/ai.service";
import { RepositoryFactory } from "../repositories";
import {
  TaskQuerySchema,
  TaskParamsSchema,
  CreateTaskSchema,
  UpdateTaskSchema,
  TaskListResponseSchema,
  TaskResponseSchema
} from "../schemas/task.schema";
import { ErrorResponseSchema, SuccessResponseSchema } from "../schemas/common.schema";

const router = new Hono<{ Bindings: CloudflareBindings }>();

// GET /api/tasks - Get user tasks
router.get(
  "/",
  describeRoute({
    description: "Get user tasks with optional filtering",
    responses: {
      200: {
        description: "Successfully retrieved tasks",
        content: {
          "application/json": {
            schema: resolver(TaskListResponseSchema.meta({
              example: {
                success: true,
                data: [{
                  id: "123e4567-e89b-12d3-a456-426614174000",
                  userId: "user123",
                  title: "Complete project documentation",
                  description: "Write comprehensive documentation for the new feature",
                  status: "pending",
                  priority: "high",
                  createdAt: "2024-01-15T10:00:00Z",
                  updatedAt: "2024-01-15T10:00:00Z"
                }],
                pagination: {
                  total: 1,
                  limit: 10,
                  offset: 0,
                  hasMore: false
                }
              }
            }))
          }
        }
      },
      400: {
        description: "Bad request - User ID required",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("query", TaskQuerySchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const query = c.req.valid("query");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const aiService = new AIService(c.env.AI);
      const taskService = new TaskService(repositoryFactory, aiService);

      const tasks = await taskService.getUserTasks(
        userId,
        query.status || "all",
        query.limit || 10
      );

      return c.json({
        success: true,
        data: tasks,
        pagination: {
          total: tasks.length,
          limit: query.limit || 10,
          offset: query.offset || 0,
          hasMore: tasks.length === (query.limit || 10)
        }
      });
    } catch (error: any) {
      console.error("Get tasks error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

// POST /api/tasks - Create new task
router.post(
  "/",
  describeRoute({
    description: "Create a new task",
    responses: {
      201: {
        description: "Task created successfully",
        content: {
          "application/json": {
            schema: resolver(TaskResponseSchema.meta({
              example: {
                success: true,
                data: {
                  id: "123e4567-e89b-12d3-a456-426614174000",
                  userId: "user123",
                  title: "Complete project documentation",
                  description: "Write comprehensive documentation for the new feature",
                  status: "pending",
                  priority: "high",
                  createdAt: "2024-01-15T10:00:00Z",
                  updatedAt: "2024-01-15T10:00:00Z"
                }
              }
            }))
          }
        }
      },
      400: {
        description: "Bad request - validation error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("json", CreateTaskSchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const body = c.req.valid("json");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const aiService = new AIService(c.env.AI);
      const taskService = new TaskService(repositoryFactory, aiService);

      const task = await taskService.createTask(
        userId,
        body.title,
        body.description,
        body.priority,
        body.dueDate ? new Date(body.dueDate) : undefined
      );

      return c.json({
        success: true,
        data: task
      }, 201);
    } catch (error: any) {
      console.error("Create task error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

// GET /api/tasks/:id - Get specific task
router.get(
  "/:id",
  describeRoute({
    description: "Get a specific task by ID",
    responses: {
      200: {
        description: "Task retrieved successfully",
        content: {
          "application/json": {
            schema: resolver(TaskResponseSchema.meta({
              example: {
                success: true,
                data: {
                  id: "123e4567-e89b-12d3-a456-426614174000",
                  userId: "user123",
                  title: "Complete project documentation",
                  description: "Write comprehensive documentation for the new feature",
                  status: "pending",
                  priority: "high",
                  createdAt: "2024-01-15T10:00:00Z",
                  updatedAt: "2024-01-15T10:00:00Z"
                }
              }
            }))
          }
        }
      },
      400: {
        description: "Bad request - User ID required",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      404: {
        description: "Task not found",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("param", TaskParamsSchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const params = c.req.valid("param");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const aiService = new AIService(c.env.AI);
      const taskService = new TaskService(repositoryFactory, aiService);

      const task = await taskService.getTask(params.id, userId);

      if (!task) {
        return c.json({ success: false, error: "Task not found" }, 404);
      }

      return c.json({
        success: true,
        data: task
      });
    } catch (error: any) {
      console.error("Get task error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

// PUT /api/tasks/:id - Update task
router.put(
  "/:id",
  describeRoute({
    description: "Update a specific task",
    responses: {
      200: {
        description: "Task updated successfully",
        content: {
          "application/json": {
            schema: resolver(TaskResponseSchema.meta({
              example: {
                success: true,
                data: {
                  id: "123e4567-e89b-12d3-a456-426614174000",
                  userId: "user123",
                  title: "Updated task title",
                  description: "Updated task description",
                  status: "in-progress",
                  priority: "high",
                  createdAt: "2024-01-15T10:00:00Z",
                  updatedAt: "2024-01-15T11:00:00Z"
                }
              }
            }))
          }
        }
      },
      400: {
        description: "Bad request - validation error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      404: {
        description: "Task not found",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("param", TaskParamsSchema),
  zValidator("json", UpdateTaskSchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const params = c.req.valid("param");
      const body = c.req.valid("json");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const aiService = new AIService(c.env.AI);
      const taskService = new TaskService(repositoryFactory, aiService);

      const task = await taskService.updateTask(params.id, userId, body);

      return c.json({
        success: true,
        data: task
      });
    } catch (error: any) {
      console.error("Update task error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

// DELETE /api/tasks/:id - Delete task
router.delete(
  "/:id",
  describeRoute({
    description: "Delete a specific task",
    responses: {
      200: {
        description: "Task deleted successfully",
        content: {
          "application/json": {
            schema: resolver(SuccessResponseSchema.meta({
              example: {
                success: true,
                message: "Task deleted successfully"
              }
            }))
          }
        }
      },
      400: {
        description: "Bad request - User ID required",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      404: {
        description: "Task not found",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("param", TaskParamsSchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const params = c.req.valid("param");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const aiService = new AIService(c.env.AI);
      const taskService = new TaskService(repositoryFactory, aiService);

      await taskService.deleteTask(params.id, userId);

      return c.json({
        success: true,
        message: "Task deleted successfully"
      });
    } catch (error: any) {
      console.error("Delete task error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

export default router;
