import { BaseRepository } from "./base.repository";
import type { ChristianEvent } from "@/types";
import { DrizzleDB, christianEvents } from "../database/connection";
import { eq, and, asc } from "drizzle-orm";

export class EventRepository extends BaseRepository<ChristianEvent> {
  constructor(db: DrizzleDB) {
    super(db);
  }

  // Create a new event
  async create(data: Partial<ChristianEvent>): Promise<ChristianEvent> {
    try {
      const mappedData = this.mapToDb(data);

      await this.db.insert(christianEvents).values(mappedData);

      const result = await this.db
        .select()
        .from(christianEvents)
        .where(eq(christianEvents.id, mappedData.id))
        .limit(1);

      return this.mapFromDb(result[0]);
    } catch (error) {
      console.error("Error creating event:", error);
      throw error;
    }
  }

  // Find event by ID
  async findById(id: string): Promise<ChristianEvent | null> {
    try {
      const result = await this.db
        .select()
        .from(christianEvents)
        .where(eq(christianEvents.id, id))
        .limit(1);

      return result.length > 0 ? this.mapFromDb(result[0]) : null;
    } catch (error) {
      console.error("Error finding event by ID:", error);
      throw error;
    }
  }

  // Find all events
  async findAll(): Promise<ChristianEvent[]> {
    try {
      const results = await this.db
        .select()
        .from(christianEvents)
        .where(eq(christianEvents.active, true))
        .orderBy(asc(christianEvents.name));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding all events:", error);
      throw error;
    }
  }

  // Find events by type
  async findByType(type: ChristianEvent["type"]): Promise<ChristianEvent[]> {
    try {
      const results = await this.db
        .select()
        .from(christianEvents)
        .where(
          and(
            eq(christianEvents.type, type),
            eq(christianEvents.active, true)
          )!
        )
        .orderBy(asc(christianEvents.name));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding events by type:", error);
      throw error;
    }
  }

  // Update event
  async update(id: string, data: Partial<ChristianEvent>): Promise<ChristianEvent | null> {
    try {
      const mappedData = this.mapToDb(data);
      mappedData.updatedAt = this.getCurrentTimestamp();

      await this.db
        .update(christianEvents)
        .set(mappedData)
        .where(eq(christianEvents.id, id));

      return this.findById(id);
    } catch (error) {
      console.error("Error updating event:", error);
      throw error;
    }
  }

  // Delete event
  async delete(id: string): Promise<boolean> {
    try {
      await this.db
        .delete(christianEvents)
        .where(eq(christianEvents.id, id));

      return true;
    } catch (error) {
      console.error("Error deleting event:", error);
      throw error;
    }
  }

  protected mapFromDb(row: any): ChristianEvent {
    return {
      id: row.id,
      name: row.name,
      type: row.type,
      fixedDate: row.fixedDate || undefined,
      calculation: row.calculation || undefined,
      description: row.description || undefined,
      reminderDays: row.reminderDays ? JSON.parse(row.reminderDays) : [1, 7],
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      isActive: Boolean(row.active)
    };
  }

  protected mapToDb(data: Partial<ChristianEvent>): any {
    const mapped: any = {};

    if (data.id !== undefined) {
      mapped.id = data.id;
    }
    if (data.name !== undefined) {
      mapped.name = data.name;
    }
    if (data.type !== undefined) {
      mapped.type = data.type;
    }
    if (data.fixedDate !== undefined) {
      mapped.fixedDate = data.fixedDate;
    }
    if (data.calculation !== undefined) {
      mapped.calculation = data.calculation;
    }
    if (data.description !== undefined) {
      mapped.description = data.description;
    }
    if (data.reminderDays !== undefined) {
      mapped.reminderDays = JSON.stringify(data.reminderDays);
    }
    if (data.createdAt !== undefined) {
      mapped.createdAt = data.createdAt;
    }
    if (data.updatedAt !== undefined) {
      mapped.updatedAt = data.updatedAt;
    }
    if (data.isActive !== undefined) {
      mapped.active = data.isActive;
    }

    // Set defaults for new events
    if (!mapped.id) {
      mapped.id = this.generateId();
    }
    if (!mapped.createdAt) {
      mapped.createdAt = this.getCurrentTimestamp();
    }
    if (!mapped.updatedAt) {
      mapped.updatedAt = this.getCurrentTimestamp();
    }
    if (mapped.active === undefined) {
      mapped.active = true;
    }
    if (!mapped.reminderDays) {
      mapped.reminderDays = JSON.stringify([1, 7]);
    }
    if (!mapped.recommendations) {
      mapped.recommendations = JSON.stringify([]);
    }

    return mapped;
  }

  // Find upcoming events within specified days
  async findUpcoming(days: number = 30): Promise<ChristianEvent[]> {
    try {
      // For now, get all active events and filter in memory
      // This could be optimized later with proper date calculations
      const results = await this.db
        .select()
        .from(christianEvents)
        .where(eq(christianEvents.active, true))
        .orderBy(asc(christianEvents.name))
        .execute();

      const events = results.map(row => this.mapFromDb(row));

      // Filter events that occur within the next 'days' days
      const today = new Date();
      const endDate = new Date(today);
      endDate.setDate(endDate.getDate() + days);

      return events.filter(event => {
        // For fixed date events, check if they occur within the date range
        if (event.type === "fixed" && event.fixedDate) {
          const eventDate = new Date(today.getFullYear(), event.fixedDate.month - 1, event.fixedDate.day);

          // If the event has already passed this year, check next year
          if (eventDate < today) {
            eventDate.setFullYear(eventDate.getFullYear() + 1);
          }

          return eventDate <= endDate;
        }

        // For calculated events, we would need more complex logic
        // For now, include all calculated events
        return event.type === "calculated";
      });
    } catch (error) {
      console.error("Error finding upcoming events:", error);
      throw error;
    }
  }
}
