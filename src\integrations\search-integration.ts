/**
 * Search Integration Service
 * Provides search functionality for tasks, web content, and knowledge base
 */

import { BaseIntegration, IntegrationConfig, IntegrationResponse } from "./base-integration";

export interface SearchOptions {
  query: string;
  type?: "web" | "tasks" | "knowledge" | "all";
  limit?: number;
  language?: string;
  safeSearch?: boolean;
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  filters?: {
    domain?: string;
    fileType?: string;
    category?: string;
  };
}

export interface SearchResult {
  id: string;
  title: string;
  description: string;
  url?: string;
  type: "web" | "task" | "knowledge" | "document";
  relevanceScore: number;
  source: string;
  timestamp?: Date;
  metadata?: {
    author?: string;
    tags?: string[];
    category?: string;
    language?: string;
  };
}

export interface SearchResponse {
  results: SearchResult[];
  totalCount: number;
  query: string;
  searchTime: number;
  suggestions?: string[];
}

export class SearchIntegration extends BaseIntegration {
  private repositoryFactory: any; // Will be injected for internal search

  constructor(config: IntegrationConfig, repositoryFactory?: any) {
    super(config);
    this.repositoryFactory = repositoryFactory;
  }

  async initialize(): Promise<void> {
    this.validateConfig();
    console.log("Search integration initialized");
  }

  async cleanup(): Promise<void> {
    console.log("Search integration cleaned up");
  }

  /**
   * Perform comprehensive search across multiple sources
   */
  async search(options: SearchOptions): Promise<IntegrationResponse<SearchResponse>> {
    try {
      const startTime = Date.now();
      const results: SearchResult[] = [];

      // Determine search types to perform
      const searchTypes = options.type === "all"
        ? ["web", "tasks", "knowledge"]
        : [options.type || "web"];

      // Perform searches in parallel
      const searchPromises = searchTypes.map(type => {
        switch (type) {
        case "web":
          return this.searchWeb(options);
        case "tasks":
          return this.searchTasks(options);
        case "knowledge":
          return this.searchKnowledge(options);
        default:
          return Promise.resolve([]);
        }
      });

      const searchResults = await Promise.allSettled(searchPromises);

      // Combine results
      searchResults.forEach((result, index) => {
        if (result.status === "fulfilled") {
          results.push(...result.value);
        } else {
          console.error(`Search failed for type ${searchTypes[index]}:`, result.reason);
        }
      });

      // Sort by relevance score
      results.sort((a, b) => b.relevanceScore - a.relevanceScore);

      // Apply limit
      const limitedResults = results.slice(0, options.limit || 10);

      const searchTime = Date.now() - startTime;

      return {
        success: true,
        data: {
          results: limitedResults,
          totalCount: results.length,
          query: options.query,
          searchTime,
          suggestions: await this.generateSearchSuggestions(options.query)
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Search failed"
      };
    }
  }

  /**
   * Search web content using DuckDuckGo Instant Answer API (free)
   */
  private async searchWeb(options: SearchOptions): Promise<SearchResult[]> {
    try {
      // Use DuckDuckGo Instant Answer API (no API key required)
      const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(options.query)}&format=json&no_html=1&skip_disambig=1`;

      const response = await this.makeRequest<any>(url);

      if (!response.success || !response.data) {
        return [];
      }

      const results: SearchResult[] = [];
      const data = response.data;

      // Process instant answer
      if (data.Answer) {
        results.push({
          id: `instant_${Date.now()}`,
          title: "Instant Answer",
          description: data.Answer,
          type: "web",
          relevanceScore: 0.9,
          source: "DuckDuckGo",
          url: data.AnswerURL || undefined,
          metadata: {
            category: "instant_answer"
          }
        });
      }

      // Process abstract
      if (data.Abstract) {
        results.push({
          id: `abstract_${Date.now()}`,
          title: data.Heading || "Abstract",
          description: data.Abstract,
          type: "web",
          relevanceScore: 0.8,
          source: data.AbstractSource || "DuckDuckGo",
          url: data.AbstractURL || undefined,
          metadata: {
            category: "abstract"
          }
        });
      }

      // Process related topics
      if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
        data.RelatedTopics.slice(0, 3).forEach((topic: any, index: number) => {
          if (topic.Text && topic.FirstURL) {
            results.push({
              id: `related_${Date.now()}_${index}`,
              title: topic.Text.split(" - ")[0] || "Related Topic",
              description: topic.Text,
              type: "web",
              relevanceScore: 0.7 - (index * 0.1),
              source: "DuckDuckGo",
              url: topic.FirstURL,
              metadata: {
                category: "related_topic"
              }
            });
          }
        });
      }

      return results;

    } catch (error) {
      console.error("Web search failed:", error);
      return [];
    }
  }

  /**
   * Search internal tasks
   */
  private async searchTasks(options: SearchOptions): Promise<SearchResult[]> {
    try {
      if (!this.repositoryFactory) {
        return [];
      }

      const taskRepo = this.repositoryFactory.getTaskRepository();
      const tasks = await taskRepo.findAll();

      const query = options.query.toLowerCase();
      const matchingTasks = tasks.filter((task: any) =>
        task.title.toLowerCase().includes(query) ||
        (task.description && task.description.toLowerCase().includes(query)) ||
        (task.tags && task.tags.some((tag: any) => tag.toLowerCase().includes(query)))
      );

      return matchingTasks.map((task: any, index: any) => ({
        id: `task_${task.id}`,
        title: task.title,
        description: task.description || "No description",
        type: "task" as const,
        relevanceScore: this.calculateTaskRelevance(task, query),
        source: "Internal Tasks",
        timestamp: task.createdAt,
        metadata: {
          category: task.category,
          tags: task.tags,
          author: "User"
        }
      }));

    } catch (error) {
      console.error("Task search failed:", error);
      return [];
    }
  }

  /**
   * Search knowledge base (placeholder for future implementation)
   */
  private async searchKnowledge(options: SearchOptions): Promise<SearchResult[]> {
    try {
      // Placeholder for knowledge base search
      // This could integrate with:
      // - Notion API
      // - Google Drive API
      // - Confluence API
      // - Custom knowledge base

      const knowledgeResults: SearchResult[] = [
        {
          id: `knowledge_${Date.now()}`,
          title: "Knowledge Base Search",
          description: `Search for "${options.query}" in knowledge base (feature coming soon)`,
          type: "knowledge",
          relevanceScore: 0.5,
          source: "Knowledge Base",
          metadata: {
            category: "placeholder"
          }
        }
      ];

      return knowledgeResults;

    } catch (error) {
      console.error("Knowledge search failed:", error);
      return [];
    }
  }

  /**
   * Calculate task relevance score
   */
  private calculateTaskRelevance(task: any, query: string): number {
    let score = 0;
    const queryWords = query.toLowerCase().split(" ");

    // Title match (highest weight)
    const titleWords = task.title.toLowerCase().split(" ");
    const titleMatches = queryWords.filter(word =>
      titleWords.some((titleWord: any) => titleWord.includes(word))
    ).length;
    score += (titleMatches / queryWords.length) * 0.6;

    // Description match
    if (task.description) {
      const descWords = task.description.toLowerCase().split(" ");
      const descMatches = queryWords.filter(word =>
        descWords.some((descWord: any) => descWord.includes(word))
      ).length;
      score += (descMatches / queryWords.length) * 0.3;
    }

    // Tag match
    if (task.tags && task.tags.length > 0) {
      const tagMatches = queryWords.filter(word =>
        task.tags.some((tag: string) => tag.toLowerCase().includes(word))
      ).length;
      score += (tagMatches / queryWords.length) * 0.1;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Generate search suggestions
   */
  private async generateSearchSuggestions(query: string): Promise<string[]> {
    try {
      // Simple suggestion generation based on common patterns
      const suggestions: string[] = [];

      // Add common task-related suggestions
      if (query.length > 2) {
        const taskSuggestions = [
          `${query} tasks`,
          `${query} deadline`,
          `${query} priority`,
          `how to ${query}`,
          `${query} tutorial`
        ];
        suggestions.push(...taskSuggestions.slice(0, 3));
      }

      return suggestions;

    } catch (error) {
      console.error("Failed to generate suggestions:", error);
      return [];
    }
  }

  /**
   * Get search analytics
   */
  async getSearchAnalytics(): Promise<IntegrationResponse<any>> {
    // try {
    // Placeholder for search analytics
    return {
      success: true,
      data: {
        totalSearches: 0,
        popularQueries: [],
        averageResponseTime: 0,
        successRate: 100
      }
    };
    // } catch (error) {
    //   return {
    //     success: false,
    //     error: error instanceof Error ? error.message : "Analytics failed"
    //   };
    // }
  }
}
