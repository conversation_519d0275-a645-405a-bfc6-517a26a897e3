import * as z from "zod";

// User preferences schema
export const UserPreferencesSchema = z.object({
  timezone: z.string().default("UTC"),
  notificationTime: z.string().default("09:00"),
  mode: z.enum(["chat", "tool"]).default("chat")
});

// User schema
export const UserSchema = z.object({
  id: z.string(), // Telegram user ID
  username: z.string().optional(),
  firstName: z.string(),
  lastName: z.string().optional(),
  createdAt: z.iso.datetime(),
  lastActive: z.iso.datetime(),
  preferences: UserPreferencesSchema
});

// Task priority enum (includes 'urgent' to fix inconsistency)
export const TaskPrioritySchema = z.enum(["low", "medium", "high", "urgent"]);

// Task status enum
export const TaskStatusSchema = z.enum(["pending", "in-progress", "completed", "cancelled"]);

// Task metadata schema (enhanced to match core types)
export const TaskMetadataSchema = z.object({
  source: z.enum(["direct", "forwarded", "ai-generated"]).default("direct"),
  originalMessage: z.string().optional(),
  tags: z.array(z.string()).optional(),
  messageId: z.number().optional(),
  attachments: z.array(z.string()).optional(),
  aiProcessed: z.boolean().optional(),
  originalText: z.string().optional()
  // Allow additional flexible metadata fields
}).passthrough().optional();

// Attachment schema
export const AttachmentSchema = z.object({
  id: z.string().uuid(),
  taskId: z.string().uuid(),
  type: z.enum(["document", "photo", "audio", "video"]),
  fileId: z.string(),
  fileName: z.string().optional(),
  mimeType: z.string().optional(),
  fileSize: z.number().optional(),
  uploadedAt: z.iso.datetime()
});

// Base task schema (enhanced to match core types)
export const TaskSchema = z.object({
  id: z.string().uuid(),
  userId: z.string(),
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  status: TaskStatusSchema.default("pending"),
  priority: TaskPrioritySchema.default("medium"),
  dueDate: z.iso.datetime().optional(),
  reminderDate: z.iso.datetime().optional(),
  createdAt: z.iso.datetime(),
  updatedAt: z.iso.datetime(),
  completedAt: z.iso.datetime().optional(),
  metadata: TaskMetadataSchema,
  attachments: z.array(AttachmentSchema).optional()
});

// Create task request schema
export const CreateTaskSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  priority: TaskPrioritySchema.optional(),
  dueDate: z.iso.datetime().optional(),
  reminderDate: z.iso.datetime().optional(),
  tags: z.array(z.string()).optional(),
  metadata: TaskMetadataSchema
});

// Update task request schema
export const UpdateTaskSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().max(1000).optional(),
  priority: TaskPrioritySchema.optional(),
  status: TaskStatusSchema.optional(),
  dueDate: z.iso.datetime().optional(),
  reminderDate: z.iso.datetime().optional(),
  tags: z.array(z.string()).optional(),
  metadata: TaskMetadataSchema
});

// Query parameters schema
export const TaskQuerySchema = z.object({
  status: z.enum(["pending", "in-progress", "completed", "overdue", "all"]).optional(),
  priority: TaskPrioritySchema.optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
  offset: z.coerce.number().min(0).optional(),
  search: z.string().optional(),
  tags: z.string().optional() // Comma-separated tags
});

// Task ID parameter schema
export const TaskParamsSchema = z.object({
  id: z.string().uuid()
});

// Success response schemas
export const TaskResponseSchema = z.object({
  success: z.boolean(),
  data: TaskSchema,
  message: z.string().optional()
});

export const TaskListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(TaskSchema),
  pagination: z.object({
    total: z.number(),
    limit: z.number(),
    offset: z.number(),
    hasMore: z.boolean()
  }),
  message: z.string().optional()
});


// Additional schemas for comprehensive type coverage

// AI Service types
export const FormattedTaskSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  priority: TaskPrioritySchema.optional(),
  dueDate: z.iso.datetime().optional()
});

export const TaskMetadataExtractionSchema = z.object({
  priority: TaskPrioritySchema.optional(),
  dueDate: z.iso.datetime().optional(),
  tags: z.array(z.string()).optional(),
  category: z.string().optional(),
  urgency: z.number().optional()
});

// External integration types
export const CalendarEventSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  startDate: z.iso.datetime(),
  endDate: z.iso.datetime(),
  location: z.string().optional()
});

export const CalendarEventInputSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  startDate: z.iso.datetime(),
  endDate: z.iso.datetime(),
  location: z.string().optional()
});

export const SearchResultSchema = z.object({
  title: z.string(),
  url: z.string(),
  snippet: z.string().optional(),
  relevance: z.number().optional()
});

export const SearchOptionsSchema = z.object({
  limit: z.number().optional(),
  type: z.enum(["web", "images", "news"]).optional(),
  dateRange: z.object({
    start: z.iso.datetime(),
    end: z.iso.datetime()
  }).optional()
});

// Input types for creating entities
export const CreateTaskInputSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  priority: TaskPrioritySchema.optional(),
  dueDate: z.iso.datetime().optional(),
  reminderDate: z.iso.datetime().optional(),
  metadata: TaskMetadataSchema
});

// Type aliases for convenience
export const TaskStatusEnum = TaskStatusSchema;
export const TaskPriorityEnum = TaskPrioritySchema;

// Export types (now as single source of truth)
export type User = z.infer<typeof UserSchema>;
export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
export type Task = z.infer<typeof TaskSchema>;
export type Attachment = z.infer<typeof AttachmentSchema>;
export type CreateTask = z.infer<typeof CreateTaskSchema>;
export type UpdateTask = z.infer<typeof UpdateTaskSchema>;
export type TaskQuery = z.infer<typeof TaskQuerySchema>;
export type TaskParams = z.infer<typeof TaskParamsSchema>;
export type TaskResponse = z.infer<typeof TaskResponseSchema>;
export type TaskListResponse = z.infer<typeof TaskListResponseSchema>;

// Additional type exports
export type FormattedTask = z.infer<typeof FormattedTaskSchema>;
export type TaskMetadata = z.infer<typeof TaskMetadataExtractionSchema>;
export type CalendarEvent = z.infer<typeof CalendarEventSchema>;
export type CalendarEventInput = z.infer<typeof CalendarEventInputSchema>;
export type SearchResult = z.infer<typeof SearchResultSchema>;
export type SearchOptions = z.infer<typeof SearchOptionsSchema>;
export type CreateTaskInput = z.infer<typeof CreateTaskInputSchema>;

// Type aliases for convenience
export type TaskStatus = z.infer<typeof TaskStatusSchema>;
export type TaskPriority = z.infer<typeof TaskPrioritySchema>;
