// Environment variables for Cloudflare Workers
interface Env {
  // Telegram configuration (stored as secrets)
  TELEGRAM_BOT_TOKEN: string;
  TELEGRAM_WEBHOOK_SECRET: string;

  // Cloudflare configuration
  CLOUDFLARE_ACCOUNT_ID: string;
  ENVIRONMENT: "development" | "production";

  // Cloudflare services
  BOT_KV: KVNamespace;
  BOT_DB: D1Database;
  AI: Ai;

  // Static assets
  ASSETS: Fetcher;

  // Durable Objects (if used in future)
  // TASK_MANAGER: DurableObjectNamespace;

  [key: string]: any;
}
