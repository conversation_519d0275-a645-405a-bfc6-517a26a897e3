// Search Wizard Scene for finding tasks, birthdays, and events

import { Scenes, Composer, Markup } from "telegraf";
import { TBotContext } from "../core/types";
import { BotServices } from "../core/core";
import { SCENES } from "../constants";

interface SearchWizardSession extends Scenes.WizardSessionData {
  searchData: {
    type?: "tasks" | "birthdays" | "events" | "all";
    query?: string;
    filters?: {
      status?: string;
      priority?: string;
      dateRange?: string;
    };
    results?: any[];
  };
}

export class SearchWizard {
  private services: BotServices;

  constructor(services: BotServices) {
    this.services = services;
  }

  createScene(): Scenes.WizardScene<TBotContext> {
    const scene = new Scenes.WizardScene<TBotContext>(
      SCENES.SEARCH,
      this.stepSearchType.bind(this),
      this.stepSearchQuery.bind(this),
      this.stepFilters.bind(this),
      this.stepResults.bind(this)
    );

    scene.command("cancel", this.handleCancel.bind(this));
    scene.action("cancel", this.handleCancel.bind(this));
    scene.action("back", this.handleBack.bind(this));
    scene.action(/^search_/, this.handleSearchTypeSelection.bind(this));
    scene.action(/^filter_/, this.handleFilterSelection.bind(this));

    return scene;
  }

  private async stepSearchType(ctx: TBotContext): Promise<void> {
    ctx.wizard.state = { searchData: {} } as SearchWizardSession;

    const message = `🔍 *Search Your Data*

What would you like to search for?`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("📝 Tasks", "search_tasks"),
          Markup.button.callback("🎂 Birthdays", "search_birthdays")
        ],
        [
          Markup.button.callback("✝️ Events", "search_events"),
          Markup.button.callback("🔍 Everything", "search_all")
        ],
        [Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    ctx.wizard.next();
  }

  private async stepSearchQuery(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as SearchWizardSession;

    if (!wizardState.searchData.type) {
      await ctx.reply("⚠️ Please select what to search for first.");
      ctx.wizard.back();
    }

    const typeNames = {
      tasks: "tasks",
      birthdays: "birthdays",
      events: "events",
      all: "everything"
    };

    const message = `🔍 *Search ${typeNames[wizardState.searchData.type].charAt(0).toUpperCase() + typeNames[wizardState.searchData.type].slice(1)}*

Enter your search query:

*Examples:*
${this.getSearchExamples(wizardState.searchData.type)}`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    ctx.wizard.next();
  }

  private async stepFilters(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as SearchWizardSession;

    if (ctx.message && "text" in ctx.message) {
      const query = ctx.message.text.trim();

      if (query.length < 2) {
        await ctx.reply("⚠️ Search query must be at least 2 characters long. Please try again:");
        return;
      }

      wizardState.searchData.query = query;
    } else {
      await ctx.reply("⚠️ Please enter a text search query:");
      return;
    }

    // Show filter options based on search type
    await this.showFilterOptions(ctx, wizardState);

    ctx.wizard.next();
  }

  private async stepResults(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as SearchWizardSession;

    // Perform the search
    const results = await this.performSearch(ctx, wizardState);
    wizardState.searchData.results = results;

    if (results.length === 0) {
      await ctx.reply(`🔍 *No Results Found*

No ${wizardState.searchData.type} found matching "${wizardState.searchData.query}".

Try:
• Different keywords
• Broader search terms
• Checking spelling`, {
        parse_mode: "Markdown",
        ...Markup.inlineKeyboard([
          [Markup.button.callback("🔄 New Search", "new_search")],
          [Markup.button.callback("❌ Close", "cancel")]
        ])
      });
      return ctx.scene.leave();
    }

    await this.displayResults(ctx, wizardState);
    return ctx.scene.leave();
  }

  private async showFilterOptions(ctx: TBotContext, wizardState: SearchWizardSession): Promise<void> {
    const type = wizardState.searchData.type!;

    const message = `🔍 *Search: "${wizardState.searchData.query}"*

Add filters to narrow your search (optional):`;

    const buttons: any[] = [];

    if (type === "tasks" || type === "all") {
      buttons.push([
        Markup.button.callback("⏳ Pending Only", "filter_status_pending"),
        Markup.button.callback("✅ Completed Only", "filter_status_completed")
      ]);
      buttons.push([
        Markup.button.callback("🔴 High Priority", "filter_priority_high"),
        Markup.button.callback("🟡 Medium Priority", "filter_priority_medium")
      ]);
    }

    if (type === "birthdays" || type === "events" || type === "all") {
      buttons.push([
        Markup.button.callback("📅 This Month", "filter_date_month"),
        Markup.button.callback("📆 This Year", "filter_date_year")
      ]);
    }

    buttons.push([
      Markup.button.callback("🔍 Search Now", "search_now"),
      Markup.button.callback("❌ Cancel", "cancel")
    ]);

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard(buttons)
    });
  }

  private async performSearch(ctx: TBotContext, wizardState: SearchWizardSession): Promise<any[]> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return [];
    }

    const { type, query, filters } = wizardState.searchData;
    let results: any[] = [];

    if (!query) {
      return [];
    }

    try {
      switch (type) {
        case "tasks":
          results = await this.searchTasks(userId, query, filters);
          break;
        case "birthdays":
          results = await this.searchBirthdays(userId, query, filters);
          break;
        case "events":
          results = await this.searchEvents(userId, query, filters);
          break;
        case "all":
          const [tasks, birthdays, events] = await Promise.all([this.searchTasks(userId, query, filters), this.searchBirthdays(userId, query, filters), this.searchEvents(userId, query, filters)
          ]);
          results = [
            ...tasks.map(t => ({ ...t, type: "task" })),
            ...birthdays.map(b => ({ ...b, type: "birthday" })),
            ...events.map(e => ({ ...e, type: "event" }))
          ];
          break;
      }

      // Track search analytics
      await this.services.sessionManager.updateAnalytics(ctx, "search_performed");

      return results;
    } catch (error) {
      console.error("Search error:", error);
      return [];
    }
  }

  private async searchTasks(userId: string, query: string, filters?: any): Promise<any[]> {
    // This would use the TaskService to search tasks
    // For now, return mock results
    return [
      {
        id: "1",
        title: "Buy groceries",
        description: "Get milk, bread, and eggs",
        status: "pending",
        priority: "medium",
        createdAt: new Date()
      },
      {
        id: "2",
        title: "Grocery shopping list",
        description: "Weekly grocery run",
        status: "completed",
        priority: "low",
        createdAt: new Date()
      }
    ].filter(task =>
      task.title.toLowerCase().includes(query.toLowerCase()) ||
      task.description.toLowerCase().includes(query.toLowerCase())
    );
  }

  private async searchBirthdays(userId: string, query: string, filters?: any): Promise<any[]> {
    // This would use the BirthdayService to search birthdays
    return [
      {
        id: "1",
        name: "John Smith",
        day: 15,
        month: 3,
        year: 1990
      }
    ].filter(birthday =>
      birthday.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  private async searchEvents(userId: string, query: string, filters?: any): Promise<any[]> {
    // This would use the EventService to search events
    return [
      {
        id: "1",
        name: "Easter Sunday",
        date: "2024-03-31",
        type: "christian"
      }
    ].filter(event =>
      event.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  private async displayResults(ctx: TBotContext, wizardState: SearchWizardSession): Promise<void> {
    const { type, query, results } = wizardState.searchData;
    const resultCount = results!.length;

    let message = `🔍 *Search Results*

Found ${resultCount} result${resultCount !== 1 ? "s" : ""} for "${query}"\n\n`;

    // Display first 5 results
    const displayResults = results!.slice(0, 5);

    displayResults.forEach((result, index) => {
      if (result.type === "task" || type === "tasks") {
        const statusIcon = result.status === "completed" ? "✅" : "⏳";
        const priorityIcon = result.priority === "high" ? "🔴" : result.priority === "medium" ? "🟡" : "🟢";
        message += `${index + 1}. ${statusIcon} ${priorityIcon} **${result.title}**\n`;
        if (result.description) {
          message += `   ${result.description}\n`;
        }
      } else if (result.type === "birthday" || type === "birthdays") {
        message += `${index + 1}. 🎂 **${result.name}**\n`;
        message += `   ${result.day}/${result.month}${result.year ? `/${result.year}` : ""}\n`;
      } else if (result.type === "event" || type === "events") {
        message += `${index + 1}. ✝️ **${result.name}**\n`;
        message += `   ${result.date}\n`;
      }
      message += "\n";
    });

    if (resultCount > 5) {
      message += `... and ${resultCount - 5} more result${resultCount - 5 !== 1 ? "s" : ""}`;
    }

    const buttons = [
      [Markup.button.callback("🔄 New Search", "new_search")]
    ];

    if (resultCount > 5) {
      buttons.unshift([Markup.button.callback("📋 View All Results", "view_all")]);
    }

    buttons.push([Markup.button.callback("❌ Close", "cancel")]);

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard(buttons)
    });
  }

  private getSearchExamples(type: string): string {
    switch (type) {
      case "tasks":
        return `• "grocery" - find tasks about groceries
• "meeting" - find meeting-related tasks
• "urgent" - find urgent tasks`;
      case "birthdays":
        return `• "john" - find John's birthday
• "smith" - find people with surname Smith
• "mom" - find family members`;
      case "events":
        return `• "easter" - find Easter events
• "christmas" - find Christmas events
• "sunday" - find Sunday events`;
      case "all":
        return `• "important" - find anything important
• "march" - find items related to March
• "family" - find family-related items`;
      default:
        return "";
    }
  }

  private async handleSearchTypeSelection(ctx: TBotContext): Promise<void> {
    if (!ctx.callbackQuery || !("data" in ctx.callbackQuery)) {
      return;
    }

    const wizardState = ctx.wizard.state as SearchWizardSession;
    const type = ctx.callbackQuery.data.replace("search_", "") as any;

    wizardState.searchData.type = type;
    await ctx.answerCbQuery();
    ctx.wizard.next();
  }

  private async handleFilterSelection(ctx: TBotContext): Promise<void> {
    if (!ctx.callbackQuery || !("data" in ctx.callbackQuery)) {
      return;
    }

    const wizardState = ctx.wizard.state as SearchWizardSession;
    const filterData = ctx.callbackQuery.data.replace("filter_", "");

    if (!wizardState.searchData.filters) {
      wizardState.searchData.filters = {};
    }

    if (filterData.startsWith("status_")) {
      wizardState.searchData.filters.status = filterData.replace("status_", "");
    } else if (filterData.startsWith("priority_")) {
      wizardState.searchData.filters.priority = filterData.replace("priority_", "");
    } else if (filterData.startsWith("date_")) {
      wizardState.searchData.filters.dateRange = filterData.replace("date_", "");
    }

    await ctx.answerCbQuery(`Filter applied: ${filterData.replace("_", " ")}`);
  }

  private async handleCancel(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    await ctx.reply("🔍 Search cancelled.");
    return ctx.scene.leave();
  }

  private async handleBack(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    ctx.wizard.back();
  }
}
