/**
 * Calendar Integration Service
 * Provides calendar functionality for task scheduling and event management
 */

import { BaseIntegration, IntegrationConfig, IntegrationResponse } from "./base-integration";

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  attendees?: string[];
  isAllDay?: boolean;
  recurrence?: {
    frequency: "daily" | "weekly" | "monthly" | "yearly";
    interval?: number;
    endDate?: Date;
    count?: number;
  };
  reminders?: {
    method: "email" | "popup" | "sms";
    minutes: number;
  }[];
  status: "confirmed" | "tentative" | "cancelled";
  source: "internal" | "google" | "outlook" | "apple";
  metadata?: {
    taskId?: string;
    birthdayId?: string;
    eventId?: string;
    priority?: "low" | "medium" | "high";
    category?: string;
  };
}

export interface CalendarEventInput {
  title: string;
  description?: string;
  startTime: Date;
  endTime?: Date;
  location?: string;
  attendees?: string[];
  isAllDay?: boolean;
  reminders?: {
    method: "email" | "popup" | "sms";
    minutes: number;
  }[];
  metadata?: {
    taskId?: string;
    birthdayId?: string;
    eventId?: string;
    priority?: "low" | "medium" | "high";
    category?: string;
  };
}

export interface CalendarQuery {
  startDate: Date;
  endDate: Date;
  calendarIds?: string[];
  categories?: string[];
  searchQuery?: string;
  includeAllDay?: boolean;
}

export interface CalendarSyncResult {
  imported: number;
  updated: number;
  deleted: number;
  errors: string[];
}

export class CalendarIntegration extends BaseIntegration {
  private repositoryFactory: any;
  private internalEvents: Map<string, CalendarEvent> = new Map();

  constructor(config: IntegrationConfig, repositoryFactory?: any) {
    super(config);
    this.repositoryFactory = repositoryFactory;
  }

  async initialize(): Promise<void> {
    this.validateConfig();
    await this.loadInternalEvents();
    console.log("Calendar integration initialized");
  }

  async cleanup(): Promise<void> {
    this.internalEvents.clear();
    console.log("Calendar integration cleaned up");
  }

  /**
   * Get events within a date range
   */
  async getEvents(query: CalendarQuery): Promise<IntegrationResponse<CalendarEvent[]>> {
    try {
      const events: CalendarEvent[] = [];

      // Get internal events
      const internalEvents = await this.getInternalEvents(query);
      events.push(...internalEvents);

      // Get external calendar events (placeholder for future implementation)
      const externalEvents = await this.getExternalEvents(query);
      events.push(...externalEvents);

      // Sort by start time
      events.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());

      // Apply search filter if provided
      let filteredEvents = events;
      if (query.searchQuery) {
        const searchLower = query.searchQuery.toLowerCase();
        filteredEvents = events.filter(event =>
          event.title.toLowerCase().includes(searchLower) ||
          (event.description && event.description.toLowerCase().includes(searchLower)) ||
          (event.location && event.location.toLowerCase().includes(searchLower))
        );
      }

      return {
        success: true,
        data: filteredEvents
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get events"
      };
    }
  }

  /**
   * Create a new calendar event
   */
  async createEvent(eventInput: CalendarEventInput): Promise<IntegrationResponse<CalendarEvent>> {
    try {
      const event: CalendarEvent = {
        id: this.generateEventId(),
        title: eventInput.title,
        description: eventInput.description,
        startTime: eventInput.startTime,
        endTime: eventInput.endTime || new Date(eventInput.startTime.getTime() + 60 * 60 * 1000), // 1 hour default
        location: eventInput.location,
        attendees: eventInput.attendees || [],
        isAllDay: eventInput.isAllDay || false,
        reminders: eventInput.reminders || [{ method: "popup", minutes: 15 }],
        status: "confirmed",
        source: "internal",
        metadata: eventInput.metadata
      };

      // Store internally
      this.internalEvents.set(event.id, event);

      // Sync with external calendars if configured
      await this.syncToExternalCalendars(event);

      return {
        success: true,
        data: event
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create event"
      };
    }
  }

  /**
   * Update an existing calendar event
   */
  async updateEvent(eventId: string, updates: Partial<CalendarEventInput>): Promise<IntegrationResponse<CalendarEvent>> {
    try {
      const existingEvent = this.internalEvents.get(eventId);
      if (!existingEvent) {
        return {
          success: false,
          error: "Event not found"
        };
      }

      const updatedEvent: CalendarEvent = {
        ...existingEvent,
        ...updates,
        id: eventId // Ensure ID doesn't change
      };

      this.internalEvents.set(eventId, updatedEvent);

      // Sync with external calendars if configured
      await this.syncToExternalCalendars(updatedEvent);

      return {
        success: true,
        data: updatedEvent
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update event"
      };
    }
  }

  /**
   * Delete a calendar event
   */
  async deleteEvent(eventId: string): Promise<IntegrationResponse<boolean>> {
    try {
      const event = this.internalEvents.get(eventId);
      if (!event) {
        return {
          success: false,
          error: "Event not found"
        };
      }

      this.internalEvents.delete(eventId);

      // Delete from external calendars if configured
      await this.deleteFromExternalCalendars(eventId);

      return {
        success: true,
        data: true
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete event"
      };
    }
  }

  /**
   * Create calendar event from task
   */
  async createEventFromTask(task: any): Promise<IntegrationResponse<CalendarEvent>> {
    try {
      const eventInput: CalendarEventInput = {
        title: `📝 ${task.title}`,
        description: task.description || "Task deadline reminder",
        startTime: task.dueDate ? new Date(task.dueDate) : new Date(),
        endTime: task.dueDate ? new Date(new Date(task.dueDate).getTime() + 30 * 60 * 1000) : undefined, // 30 min
        reminders: [
          { method: "popup", minutes: 60 }, // 1 hour before
          { method: "popup", minutes: 15 }  // 15 minutes before
        ],
        metadata: {
          taskId: task.id,
          priority: task.priority,
          category: "task"
        }
      };

      return await this.createEvent(eventInput);

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create event from task"
      };
    }
  }

  /**
   * Create calendar event from birthday
   */
  async createEventFromBirthday(birthday: any): Promise<IntegrationResponse<CalendarEvent>> {
    try {
      const birthdayDate = new Date();
      birthdayDate.setMonth(birthday.month - 1);
      birthdayDate.setDate(birthday.day);

      // If birthday has passed this year, set for next year
      if (birthdayDate < new Date()) {
        birthdayDate.setFullYear(birthdayDate.getFullYear() + 1);
      }

      const eventInput: CalendarEventInput = {
        title: `🎂 ${birthday.name}'s Birthday`,
        description: `Birthday celebration for ${birthday.name}`,
        startTime: birthdayDate,
        isAllDay: true,
        reminders: [
          { method: "popup", minutes: 24 * 60 }, // 1 day before
          { method: "popup", minutes: 60 }       // 1 hour before
        ],
        metadata: {
          birthdayId: birthday.id,
          category: "birthday"
        }
      };

      return await this.createEvent(eventInput);

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create event from birthday"
      };
    }
  }

  /**
   * Get internal events from tasks, birthdays, and Christian events
   */
  private async getInternalEvents(query: CalendarQuery): Promise<CalendarEvent[]> {
    const events: CalendarEvent[] = [];

    try {
      if (!this.repositoryFactory) {
        return Array.from(this.internalEvents.values()).filter(event =>
          event.startTime >= query.startDate && event.startTime <= query.endDate
        );
      }

      // Get tasks with due dates
      const taskRepo = this.repositoryFactory.getTaskRepository();
      const tasks = await taskRepo.findAll();

      tasks.forEach((task: any) => {
        if (task.dueDate) {
          const dueDate = new Date(task.dueDate);
          if (dueDate >= query.startDate && dueDate <= query.endDate) {
            events.push({
              id: `task_${task.id}`,
              title: `📝 ${task.title}`,
              description: task.description,
              startTime: dueDate,
              endTime: new Date(dueDate.getTime() + 30 * 60 * 1000),
              status: "confirmed",
              source: "internal",
              metadata: {
                taskId: task.id,
                priority: task.priority,
                category: "task"
              }
            });
          }
        }
      });

      // Get birthdays
      const birthdayRepo = this.repositoryFactory.getBirthdayRepository();
      const birthdays = await birthdayRepo.findAll();

      birthdays.forEach((birthday: any) => {
        const birthdayDate = new Date(query.startDate.getFullYear(), birthday.month - 1, birthday.day);
        if (birthdayDate >= query.startDate && birthdayDate <= query.endDate) {
          events.push({
            id: `birthday_${birthday.id}`,
            title: `🎂 ${birthday.name}'s Birthday`,
            description: `Birthday celebration for ${birthday.name}`,
            startTime: birthdayDate,
            endTime: birthdayDate,
            isAllDay: true,
            status: "confirmed",
            source: "internal",
            metadata: {
              birthdayId: birthday.id,
              category: "birthday"
            }
          });
        }
      });

      // Add manually created events
      const manualEvents = Array.from(this.internalEvents.values()).filter(event =>
        event.startTime >= query.startDate && event.startTime <= query.endDate
      );
      events.push(...manualEvents);

    } catch (error) {
      console.error("Failed to get internal events:", error);
    }

    return events;
  }

  /**
   * Get external calendar events (placeholder for future implementation)
   */
  private async getExternalEvents(query: CalendarQuery): Promise<CalendarEvent[]> {
    // Placeholder for external calendar integration
    // This could integrate with:
    // - Google Calendar API
    // - Microsoft Outlook API
    // - Apple Calendar API
    // - CalDAV servers

    return [];
  }

  /**
   * Sync event to external calendars
   */
  private async syncToExternalCalendars(event: CalendarEvent): Promise<void> {
    // Placeholder for external calendar sync
    console.log(`Syncing event ${event.id} to external calendars`);
  }

  /**
   * Delete event from external calendars
   */
  private async deleteFromExternalCalendars(eventId: string): Promise<void> {
    // Placeholder for external calendar deletion
    console.log(`Deleting event ${eventId} from external calendars`);
  }

  /**
   * Load internal events from storage
   */
  private async loadInternalEvents(): Promise<void> {
    // Placeholder for loading events from persistent storage
    console.log("Loading internal events");
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `cal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get calendar statistics
   */
  async getCalendarStats(): Promise<IntegrationResponse<any>> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      const monthEvents = await this.getEvents({
        startDate: startOfMonth,
        endDate: endOfMonth
      });

      return {
        success: true,
        data: {
          totalEvents: this.internalEvents.size,
          monthlyEvents: monthEvents.data?.length || 0,
          upcomingEvents: monthEvents.data?.filter(e => e.startTime > now).length || 0,
          categories: {
            tasks: monthEvents.data?.filter(e => e.metadata?.category === "task").length || 0,
            birthdays: monthEvents.data?.filter(e => e.metadata?.category === "birthday").length || 0,
            events: monthEvents.data?.filter(e => e.metadata?.category === "event").length || 0
          }
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get calendar stats"
      };
    }
  }
}
