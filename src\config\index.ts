// Configuration management for the Novers Telegram Bot Assistant

export interface Config {
  telegram: {
    botToken: string;
    webhookSecret: string;
  };
  cloudflare: {
    accountId: string;
    environment: "development" | "production";
  };
  features: {
    aiEnabled: boolean;
    calendarIntegration: boolean;
    searchIntegration: boolean;
  };
  integrations: {
    search: {
      baseUrl: string;
      timeout: number;
      retryAttempts: number;
      rateLimitPerMinute: number;
      enabledSources: string[];
    };
    calendar: {
      baseUrl: string;
      timeout: number;
      retryAttempts: number;
      enabledProviders: string[];
    };
    enabledIntegrations: string[];
  };
  notifications: {
    defaultReminderDays: number[];
    maxNotificationsPerDay: number;
  };
  ai: {
    models: {
      textGeneration: string;
      textClassification: string;
    };
    maxTokens: number;
    temperature: number;
  };
}

export function getConfig(env: Env): Config {
  return {
    telegram: {
      botToken: env.TELEGRAM_BOT_TOKEN,
      webhookSecret: env.TELEGRAM_WEBHOOK_SECRET
    },
    cloudflare: {
      accountId: env.CLOUDFLARE_ACCOUNT_ID || "",
      environment: env.ENVIRONMENT || "development"
    },
    features: {
      aiEnabled: true,
      calendarIntegration: true, // Now implemented
      searchIntegration: true   // Now implemented
    },
    integrations: {
      search: {
        baseUrl: "https://api.duckduckgo.com",
        timeout: 10000,
        retryAttempts: 2,
        rateLimitPerMinute: 30,
        enabledSources: ["web", "tasks", "knowledge"]
      },
      calendar: {
        baseUrl: "internal",
        timeout: 5000,
        retryAttempts: 1,
        enabledProviders: ["internal"] // Future: 'google', 'outlook', 'apple'
      },
      enabledIntegrations: ["search", "calendar"]
    },
    notifications: {
      defaultReminderDays: [1, 7, 30],
      maxNotificationsPerDay: 10
    },
    ai: {
      models: {
        textGeneration: "@cf/meta/llama-2-7b-chat-int8",
        textClassification: "@cf/huggingface/distilbert-sst-2-int8"
      },
      maxTokens: 512,
      temperature: 0.7
    }
  };
}

export function validateConfig(config: Config): void {
  if (!config.telegram.botToken) {
    throw new Error("TELEGRAM_BOT_TOKEN is required");
  }

  if (!config.telegram.webhookSecret) {
    throw new Error("TELEGRAM_WEBHOOK_SECRET is required");
  }

  if (config.cloudflare.environment !== "development" && config.cloudflare.environment !== "production") {
    throw new Error("ENVIRONMENT must be either \"development\" or \"production\"");
  }
}

// Default configuration values
export const DEFAULT_CONFIG = {
  notifications: {
    defaultReminderDays: [1, 7, 30],
    maxNotificationsPerDay: 10
  },
  ai: {
    maxTokens: 512,
    temperature: 0.7
  }
} as const;
