import { Context, Next } from "hono";
import { ErrorResponse } from "@/types";

// Custom error classes
export class ValidationError extends Error {
  constructor(message: string, public details?: Record<string, any>) {
    super(message);
    this.name = "ValidationError";
  }
}

export class AuthenticationError extends Error {
  constructor(message: string = "Authentication failed") {
    super(message);
    this.name = "AuthenticationError";
  }
}

export class NotFoundError extends Error {
  constructor(message: string = "Resource not found") {
    super(message);
    this.name = "NotFoundError";
  }
}

export class ExternalServiceError extends Error {
  constructor(message: string, public service: string) {
    super(message);
    this.name = "ExternalServiceError";
  }
}

export async function errorHandler(c: Context, next: Next) {
  try {
    await next();
  } catch (err: any) {
    console.error("Error occurred:", {
      name: err.name,
      message: err.message,
      stack: err.stack,
      url: c.req.url,
      method: c.req.method,
      timestamp: new Date().toISOString()
    });

    let statusCode = 500;
    let errorCode = "INTERNAL_SERVER_ERROR";
    let errorMessage = "An unexpected error occurred";
    let details: Record<string, any> | undefined;

    // Handle specific error types
    if (err instanceof ValidationError) {
      statusCode = 400;
      errorCode = "VALIDATION_ERROR";
      errorMessage = err.message;
      details = err.details;
    } else if (err instanceof AuthenticationError) {
      statusCode = 401;
      errorCode = "AUTHENTICATION_ERROR";
      errorMessage = err.message;
    } else if (err instanceof NotFoundError) {
      statusCode = 404;
      errorCode = "NOT_FOUND";
      errorMessage = err.message;
    } else if (err instanceof ExternalServiceError) {
      statusCode = 502;
      errorCode = "EXTERNAL_SERVICE_ERROR";
      errorMessage = `${err.service}: ${err.message}`;
    } else if (err.message) {
      errorMessage = err.message;
    }

    const errorResponse: ErrorResponse = {
      success: false,
      error: errorMessage,
      code: errorCode,
      message: errorMessage,
      ...(details && { details })
    };

    return c.json(errorResponse, statusCode as any);
  }
}
