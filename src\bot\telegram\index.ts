// Production-scale Telegram bot with advanced Telegraf patterns

import { Context } from "hono";
import { Scenes } from "telegraf";
import { BotCore, BotServices } from "./core/core";
import { TBotContext } from "./core/types";
import { RBACComposers } from "./access-control/rbac-composers";
import { TaskCreationWizard } from "./scenes/task-creation-wizard";
import { BirthdayCreationWizard } from "./scenes/birthday-creation-wizard";
import { SettingsWizard } from "./scenes/settings-wizard";
import { SubscriptionWizard } from "./scenes/subscription-wizard";
import { SearchWizard } from "./scenes/search-wizard";
import { message } from "telegraf/filters";
import {
  SCENES,
  CALLBACKS,
  BUTTONS,
  MESSAGES,
  ICONS,
  COMMANDS,
  INTENT_KEYWORDS
} from "./constants";

export class TelegramBot {
  private botCore: BotCore;
  private services: BotServices;
  private rbacComposers: RBACComposers;
  private taskCreationWizard: TaskCreationWizard;
  private birthdayCreationWizard: BirthdayCreationWizard;
  private settingsWizard: SettingsWizard;
  private subscriptionWizard: SubscriptionWizard;
  private searchWizard: SearchWizard;

  constructor(env: Env) {
    const botToken = env.TELEGRAM_BOT_TOKEN || env.BOT_TOKEN;

    // Initialize production bot core
    this.botCore = new BotCore(botToken, env);
    this.services = this.botCore.getServices();

    // Initialize RBAC composers
    this.rbacComposers = new RBACComposers(this.services);

    // Initialize wizards
    this.taskCreationWizard = new TaskCreationWizard(this.services);
    this.birthdayCreationWizard = new BirthdayCreationWizard(this.services);
    this.settingsWizard = new SettingsWizard(this.services);
    this.subscriptionWizard = new SubscriptionWizard(this.services);
    this.searchWizard = new SearchWizard(this.services);

    this.setupBot();
  }

  /**
   * Setup the production bot with all features
   */
  private setupBot(): void {
    const bot = this.botCore.getBot();

    // Setup scenes
    this.setupScenes();

    // Setup role-based access control
    this.setupRBAC();

    // Setup global handlers
    this.setupGlobalHandlers();

    // Setup callback handlers
    this.setupCallbackHandlers();

    // Setup error recovery
    this.setupErrorRecovery();
  }

  /**
   * Setup wizard scenes
   */
  private setupScenes(): void {
    // Add all wizard scenes
    const taskWizardScene = this.taskCreationWizard.createScene();
    const birthdayWizardScene = this.birthdayCreationWizard.createScene();
    const settingsWizardScene = this.settingsWizard.createScene();
    const subscriptionWizardScene = this.subscriptionWizard.createScene();
    const searchWizardScene = this.searchWizard.createScene();

    this.botCore.addScene(taskWizardScene);
    this.botCore.addScene(birthdayWizardScene);
    this.botCore.addScene(settingsWizardScene);
    this.botCore.addScene(subscriptionWizardScene);
    this.botCore.addScene(searchWizardScene);
  }

  /**
   * Setup role-based access control
   */
  private setupRBAC(): void {
    const bot = this.botCore.getBot();
    const { adminComposer, paidComposer, freeComposer } = this.botCore.createRoleBasedComposers();
    const rbacComposers = this.rbacComposers;

    // Setup admin features
    const adminFeatures = rbacComposers.createAdminComposer();
    bot.use(adminFeatures);

    // Setup premium features
    const premiumFeatures = rbacComposers.createPaidComposer();
    bot.use(premiumFeatures);

    // Setup AI features with quota enforcement
    const aiFeatures = rbacComposers.createAIComposer();
    bot.use(aiFeatures);

    // Setup basic features (available to all users)
    const basicFeatures = rbacComposers.createFreeComposer();
    bot.use(basicFeatures);
  }

  /**
   * Setup global handlers that work across all user tiers
   */
  private setupGlobalHandlers(): void {
    const bot = this.botCore.getBot();

    // Global commands that bypass RBAC (essential commands)
    bot.start(async (ctx) => {
      await this.services.sessionManager.updateAnalytics(ctx, "start_command");
      // The actual handler is in RBACComposers.handleStart
    });

    bot.help(async (ctx) => {
      await this.services.sessionManager.updateAnalytics(ctx, "help_command");
      // The actual handler is in RBACComposers.handleHelp
    });

    // Handle text messages with intelligent routing
    bot.on(message("text"), async (ctx) => {
      await this.handleTextMessage(ctx);
    });

    // Handle photos, documents, and other media
    bot.on(message("photo"), async (ctx) => {
      await this.handleMediaMessage(ctx, "photo");
    });

    bot.on(message("document"), async (ctx) => {
      await this.handleMediaMessage(ctx, "document");
    });

    bot.on(message("voice"), async (ctx) => {
      await this.handleMediaMessage(ctx, "voice");
    });

    // Handle location sharing
    bot.on(message("location"), async (ctx) => {
      await this.handleLocationMessage(ctx);
    });

    // Handle contact sharing
    bot.on(message("contact"), async (ctx) => {
      await this.handleContactMessage(ctx);
    });
  }

  /**
   * Setup callback query handlers
   */
  private setupCallbackHandlers(): void {
    const bot = this.botCore.getBot();

    // Main navigation callbacks
    bot.action(CALLBACKS.TASK_CREATE, async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.TASK_CREATION);
    });

    bot.action(CALLBACKS.BIRTHDAY_ADD, async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.BIRTHDAY_CREATION);
    });

    bot.action(CALLBACKS.TASK_LIST, async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleViewTasks(ctx);
    });

    bot.action(CALLBACKS.SETTINGS_MENU, async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.SETTINGS);
    });

    bot.action(CALLBACKS.UPGRADE, async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.SUBSCRIPTION);
    });

    // Search functionality
    bot.action(CALLBACKS.SEARCH_MENU, async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.SEARCH);
    });

    bot.command(COMMANDS.SEARCH, async (ctx) => {
      await ctx.scene.enter(SCENES.SEARCH);
    });

    // Subscription callbacks
    bot.action(CALLBACKS.SUBSCRIBE_PREMIUM, async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleSubscription(ctx, "premium");
    });

    bot.action(CALLBACKS.COMPARE_PLANS, async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleComparePlans(ctx);
    });

    bot.action(CALLBACKS.UPGRADE_FAQ, async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleUpgradeFAQ(ctx);
    });

    // Settings callbacks
    bot.action(/^settings_/, async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleSettingsCallback(ctx);
    });
  }

  /**
   * Setup error recovery mechanisms
   */
  private setupErrorRecovery(): void {
    const bot = this.botCore.getBot();

    // Handle unknown commands gracefully
    bot.on("message", async (ctx, next) => {
      try {
        await next();
      } catch (error) {
        console.error("Message handling error:", error);
        await ctx.reply(MESSAGES.SOMETHING_WENT_WRONG);
      }
    });

    // Handle callback query errors
    bot.on("callback_query", async (ctx, next) => {
      try {
        await next();
      } catch (error) {
        console.error("Callback handling error:", error);
        await ctx.answerCbQuery(MESSAGES.ACTION_FAILED);
      }
    });
  }

  /**
   * Handle text messages with intelligent routing
   */
  private async handleTextMessage(ctx: TBotContext): Promise<void> {
    if (!ctx.message || !("text" in ctx.message)) {
      return;
    }

    const text = ctx.message.text;
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    // Update analytics
    await this.services.sessionManager.updateAnalytics(ctx, "text_message");

    // Check if user is in a scene (wizard flow)
    if (ctx.scene.current) {
      // Let the scene handle the message
      return;
    }

    // Check for quick task creation (natural language)
    if (this.isTaskCreationIntent(text)) {
      await this.handleQuickTaskCreation(ctx, text);
      return;
    }

    // Check for birthday mentions
    if (this.isBirthdayIntent(text)) {
      await this.handleQuickBirthdayCreation(ctx, text);
      return;
    }

    // Check for search intent
    if (this.isSearchIntent(text)) {
      await this.handleSearch(ctx, text);
      return;
    }

    // Default response for unrecognized text
    await ctx.reply(
      MESSAGES.NOT_SURE_WHAT_YOU_MEAN,
      {
        reply_markup: {
          inline_keyboard: [
            [
              { text: BUTTONS.CREATE_TASK, callback_data: CALLBACKS.TASK_CREATE },
              { text: BUTTONS.ADD_BIRTHDAY, callback_data: CALLBACKS.BIRTHDAY_ADD }
            ],
            [
              { text: BUTTONS.VIEW_TASKS, callback_data: CALLBACKS.TASK_LIST },
              { text: BUTTONS.SETTINGS, callback_data: CALLBACKS.SETTINGS_MENU }
            ]
          ]
        }
      }
    );
  }

  /**
   * Handle media messages (photos, documents, voice)
   */
  private async handleMediaMessage(ctx: TBotContext, mediaType: string): Promise<void> {
    await this.services.sessionManager.updateAnalytics(ctx, `${mediaType}_message`);

    // Check if user has premium features for media processing
    const hasPremiumFeatures = this.services.sessionManager.hasFeatureAccess(ctx, "enhanced_ai");

    if (hasPremiumFeatures) {
      const message = mediaType === "photo" ? MESSAGES.PHOTO_RECEIVED_PREMIUM :
        mediaType === "document" ? MESSAGES.DOCUMENT_RECEIVED_PREMIUM :
          MESSAGES.VOICE_RECEIVED_PREMIUM;
      await ctx.reply(message);
    } else {
      const message = mediaType === "photo" ? MESSAGES.PHOTO_RECEIVED_UPGRADE :
        mediaType === "document" ? MESSAGES.DOCUMENT_RECEIVED_UPGRADE :
          MESSAGES.VOICE_RECEIVED_UPGRADE;
      await ctx.reply(
        message,
        {
          reply_markup: {
            inline_keyboard: [
              [{ text: BUTTONS.UPGRADE, callback_data: CALLBACKS.UPGRADE }]
            ]
          }
        }
      );
    }
  }

  /**
   * Handle location messages
   */
  private async handleLocationMessage(ctx: TBotContext): Promise<void> {
    await this.services.sessionManager.updateAnalytics(ctx, "location_message");
    await ctx.reply(MESSAGES.LOCATION_RECEIVED);
  }

  /**
   * Handle contact messages
   */
  private async handleContactMessage(ctx: TBotContext): Promise<void> {
    await this.services.sessionManager.updateAnalytics(ctx, "contact_message");
    await ctx.reply(MESSAGES.CONTACT_RECEIVED);
  }

  /**
   * Handle quick task creation from natural language
   */
  private async handleQuickTaskCreation(ctx: TBotContext, text: string): Promise<void> {
    // Check quota
    const canCreateTask = await this.services.sessionManager.checkQuota(ctx, "tasks");
    if (!canCreateTask) {
      await ctx.reply(MESSAGES.TASK_LIMIT_REACHED);
      return;
    }

    try {
      // Use AI to extract task information if available
      const hasAI = this.services.sessionManager.hasFeatureAccess(ctx, "basic_ai");
      const canUseAI = hasAI && await this.services.sessionManager.checkQuota(ctx, "aiRequests");

      const task = await this.services.taskService.createTask(
        ctx.from!.id.toString(),
        text,
        undefined,
        "medium",
        undefined
      );

      // Consume quotas
      await this.services.sessionManager.consumeQuota(ctx, "tasks");
      if (canUseAI) {
        await this.services.sessionManager.consumeQuota(ctx, "aiRequests");
      }

      await ctx.reply(
        MESSAGES.TASK_CREATED_WITH_TITLE.replace("{title}", task.title),
        {
          reply_markup: {
            inline_keyboard: [
              [{ text: BUTTONS.VIEW_TASKS, callback_data: CALLBACKS.TASK_LIST }]
            ]
          }
        }
      );
    } catch (error) {
      console.error("Quick task creation error:", error);
      await ctx.reply(MESSAGES.FAILED_TO_CREATE_TASK);
    }
  }

  /**
   * Handle quick birthday creation
   */
  private async handleQuickBirthdayCreation(ctx: TBotContext, _text: string): Promise<void> {
    await ctx.reply(MESSAGES.QUICK_BIRTHDAY_DETECTED);
  }

  /**
   * Handle search functionality
   */
  private async handleSearch(ctx: TBotContext, text: string): Promise<void> {
    const query = text.replace(/^(search|find|look for)\s+/i, "");
    await ctx.reply(MESSAGES.SEARCH_FUNCTIONALITY_COMING_SOON.replace("{query}", query));
  }

  /**
   * Handle view tasks
   */
  private async handleViewTasks(ctx: TBotContext): Promise<void> {
    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      const tasks = await this.services.taskService.getUserTasks(userId, "all", 5);

      if (tasks.length === 0) {
        await ctx.reply(
          MESSAGES.NO_TASKS_CREATE_FIRST,
          {
            reply_markup: {
              inline_keyboard: [
                [{ text: BUTTONS.CREATE_TASK, callback_data: CALLBACKS.TASK_CREATE }]
              ]
            }
          }
        );
        return;
      }

      const taskList = tasks.map((task, index) => {
        const statusIcon = task.status === "completed" ? ICONS.COMPLETED :
          task.status === "in-progress" ? ICONS.IN_PROGRESS : ICONS.PENDING;
        const priorityIcon = task.priority === "high" ? ICONS.HIGH_PRIORITY :
          task.priority === "medium" ? ICONS.MEDIUM_PRIORITY : ICONS.LOW_PRIORITY;

        return `${index + 1}. ${statusIcon} ${priorityIcon} ${task.title}`;
      }).join("\n");

      await ctx.reply(
        `${MESSAGES.YOUR_TASKS_HEADER}\n\n${taskList}`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [
              [
                { text: BUTTONS.CREATE_TASK, callback_data: CALLBACKS.TASK_CREATE },
                { text: BUTTONS.VIEW_ALL, callback_data: CALLBACKS.VIEW_ALL_TASKS }
              ]
            ]
          }
        }
      );
    } catch (error) {
      console.error("View tasks error:", error);
      await ctx.reply(MESSAGES.FAILED_TO_LOAD_TASKS);
    }
  }

  /**
   * Handle subscription process
   */
  private async handleSubscription(ctx: TBotContext, _plan: string): Promise<void> {
    await ctx.scene.enter("subscription-wizard");
  }

  /**
   * Handle plan comparison
   */
  private async handleComparePlans(ctx: TBotContext): Promise<void> {
    const comparison = `
📊 *Plan Comparison*

**FREE**
• 10 tasks
• 5 birthdays  
• 3 AI requests/day
• Basic notifications
• Standard support

**PREMIUM** ($9.99/month)
• Unlimited tasks
• Unlimited birthdays
• 100 AI requests/day
• Advanced notifications
• Data export
• Task templates
• Priority support
• Custom themes
    `;

    await ctx.reply(comparison, {
      parse_mode: "Markdown",
      reply_markup: {
        inline_keyboard: [
          [{ text: BUTTONS.UPGRADE_TO_PREMIUM, callback_data: CALLBACKS.SUBSCRIBE_PREMIUM }]
        ]
      }
    });
  }

  /**
   * Handle upgrade FAQ
   */
  private async handleUpgradeFAQ(ctx: TBotContext): Promise<void> {
    const faq = `
❓ *Upgrade FAQ*

**Q: How do I upgrade?**
A: Click "Subscribe Now" and follow the payment process.

**Q: Can I cancel anytime?**
A: Yes, cancel anytime. No long-term commitments.

**Q: What payment methods do you accept?**
A: Credit cards, PayPal, and cryptocurrency.

**Q: Do I keep my data if I downgrade?**
A: Yes, but some features may be limited.

**Q: Is there a free trial?**
A: New users get 7 days of Premium features free!

Need more help? Contact support!
    `;

    await ctx.reply(faq, { parse_mode: "Markdown" });
  }

  /**
   * Handle settings callbacks
   */
  private async handleSettingsCallback(ctx: TBotContext): Promise<void> {
    if (!ctx.callbackQuery || !("data" in ctx.callbackQuery)) {
      return;
    }

    const setting = ctx.callbackQuery.data.replace("settings_", "");
    await ctx.reply(MESSAGES.SETTINGS_COMING_SOON.replace("{setting}", setting.charAt(0).toUpperCase() + setting.slice(1)));
  }

  // Intent detection helpers
  private isTaskCreationIntent(text: string): boolean {
    const lowerText = text.toLowerCase();
    return INTENT_KEYWORDS.TASK_CREATION.some(keyword => lowerText.includes(keyword)) && text.length > 10;
  }

  private isBirthdayIntent(text: string): boolean {
    const lowerText = text.toLowerCase();
    return INTENT_KEYWORDS.BIRTHDAY.some(keyword => lowerText.includes(keyword));
  }

  private isSearchIntent(text: string): boolean {
    const lowerText = text.toLowerCase();
    return INTENT_KEYWORDS.SEARCH.some(keyword => lowerText.startsWith(keyword));
  }

  /**
   * Handle webhook requests
   */
  async handleWebhook(c: Context): Promise<Response> {
    return this.botCore.handleWebhook(c);
  }

  /**
   * Get bot services for external access
   */
  getServices(): BotServices {
    return this.services;
  }
}
