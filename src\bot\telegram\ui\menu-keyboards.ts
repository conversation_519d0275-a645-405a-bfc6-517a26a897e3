import { Markup } from "telegraf";
import { BUTTONS, CALL<PERSON>CKS, CA<PERSON><PERSON><PERSON><PERSON>_PREFIXES } from "../constants";

export class MenuKeyboards {

  static getStartMainMenuKeyboard() {
    return Markup.keyboard([
      [BUTTONS.MAIN_MENU, BUTTONS.HELP]
    ]).resize().persistent();
  }
  static getMainMenuKeyboard() {
    return Markup.keyboard([
      [BUTTONS.TASKS, BUTTONS.BIRTHDAYS],
      [BUTTONS.CHRISTIAN_EVENTS, BUTTONS.NOTIFICATIONS],
      [BUTTONS.SEARCH, BUTTONS.CALENDAR],
      [BUTTONS.SETTINGS, BUTTONS.HELP]
    ]);
  }

  static getMainMenuInlineKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.TASKS, CALLBACKS.TASKS_MENU), Markup.button.callback(BUTTONS.BIRTHDAYS, CALLBACKS.BIRTHDAYS_MENU)],
      [Markup.button.callback(BUTTONS.CHRISTIAN_EVENTS, CALLBACKS.EVENTS_MENU), Markup.button.callback(BUTTONS.NOTIFICATIONS, CALLBACKS.NOTIFICATIONS_MENU)],
      [Markup.button.callback(BUTTONS.SEARCH, CALLBACKS.SEARCH_MENU), Markup.button.callback(BUTTONS.CALENDAR, CALLBACKS.CALENDAR_MENU)],
      [Markup.button.callback(BUTTONS.SETTINGS, CALLBACKS.SETTINGS_MENU), Markup.button.callback(BUTTONS.HELP, CALLBACKS.HELP_MENU)]
    ]);
  }

  static getTaskMenuKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.CREATE_TASK, CALLBACKS.TASK_CREATE)],
      [Markup.button.callback(BUTTONS.VIEW_TASKS, CALLBACKS.TASK_LIST), Markup.button.callback(BUTTONS.PENDING_TASKS, CALLBACKS.TASK_PENDING)],
      [Markup.button.callback(BUTTONS.COMPLETED_TASKS, CALLBACKS.TASK_COMPLETED), Markup.button.callback(BUTTONS.SEARCH_TASKS, CALLBACKS.TASK_SEARCH)],
      [Markup.button.callback(BUTTONS.BACK_TO_MAIN, CALLBACKS.MAIN_MENU)]
    ]);
  }

  static getBirthdayMenuKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.ADD_BIRTHDAY, CALLBACKS.BIRTHDAY_ADD)],
      [Markup.button.callback(BUTTONS.VIEW_BIRTHDAYS, CALLBACKS.BIRTHDAY_LIST), Markup.button.callback(BUTTONS.UPCOMING_BIRTHDAYS, CALLBACKS.BIRTHDAY_UPCOMING)],
      [Markup.button.callback(BUTTONS.SEARCH_BIRTHDAYS, CALLBACKS.BIRTHDAY_SEARCH)],
      [Markup.button.callback(BUTTONS.BACK_TO_MAIN, CALLBACKS.MAIN_MENU)]
    ]);
  }

  static getEventsMenuKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.VIEW_EVENTS, CALLBACKS.EVENTS_LIST)],
      [Markup.button.callback(BUTTONS.UPCOMING_EVENTS, CALLBACKS.EVENTS_UPCOMING), Markup.button.callback(BUTTONS.TODAY_EVENTS, CALLBACKS.EVENTS_TODAY)],
      [Markup.button.callback(BUTTONS.SEARCH_EVENTS, CALLBACKS.EVENTS_SEARCH)],
      [Markup.button.callback(BUTTONS.BACK_TO_MAIN, CALLBACKS.MAIN_MENU)]
    ]);
  }

  static getNotificationsMenuKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.VIEW_NOTIFICATIONS, CALLBACKS.NOTIFICATIONS_LIST)],
      [Markup.button.callback(BUTTONS.PENDING_NOTIFICATIONS, CALLBACKS.NOTIFICATIONS_PENDING), Markup.button.callback(BUTTONS.RECENT_NOTIFICATIONS, CALLBACKS.NOTIFICATIONS_RECENT)],
      [Markup.button.callback(BUTTONS.NOTIFICATION_SETTINGS, CALLBACKS.NOTIFICATIONS_SETTINGS)],
      [Markup.button.callback(BUTTONS.BACK_TO_MAIN, CALLBACKS.MAIN_MENU)]
    ]);
  }

  static getPriorityKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.HIGH_PRIORITY, CALLBACKS.PRIORITY_HIGH)],
      [Markup.button.callback(BUTTONS.MEDIUM_PRIORITY, CALLBACKS.PRIORITY_MEDIUM)],
      [Markup.button.callback(BUTTONS.LOW_PRIORITY, CALLBACKS.PRIORITY_LOW)],
      [Markup.button.callback(BUTTONS.CANCEL, CALLBACKS.CANCEL)]
    ]);
  }

  static getTaskStatusKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.STATUS_PENDING, CALLBACKS.STATUS_PENDING)],
      [Markup.button.callback(BUTTONS.STATUS_IN_PROGRESS, CALLBACKS.STATUS_IN_PROGRESS)],
      [Markup.button.callback(BUTTONS.STATUS_COMPLETED, CALLBACKS.STATUS_COMPLETED)],
      [Markup.button.callback(BUTTONS.CANCEL, CALLBACKS.CANCEL)]
    ]);
  }

  static getConfirmationKeyboard(confirmAction: string, cancelAction: string = CALLBACKS.CANCEL) {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.YES, confirmAction), Markup.button.callback(BUTTONS.NO, cancelAction)]
    ]);
  }

  static getCancelKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.CANCEL, CALLBACKS.CANCEL)]
    ]);
  }

  static getBackKeyboard(backAction: string) {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.BACK, backAction)]
    ]);
  }

  static getTaskActionKeyboard(taskId: string) {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.EDIT, `${CALLBACK_PREFIXES.TASK_EDIT}${taskId}`), Markup.button.callback(BUTTONS.STATUS, `${CALLBACK_PREFIXES.TASK_CHANGE_STATUS}${taskId}`)],
      [Markup.button.callback(BUTTONS.DELETE, `${CALLBACK_PREFIXES.TASK_DELETE}${taskId}`), Markup.button.callback(BUTTONS.DETAILS, `${CALLBACK_PREFIXES.TASK_DETAILS}${taskId}`)],
      [Markup.button.callback(BUTTONS.BACK, CALLBACKS.TASK_LIST)]
    ]);
  }

  static getBirthdayActionKeyboard(birthdayId: string) {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.EDIT, `${CALLBACK_PREFIXES.BIRTHDAY_EDIT}${birthdayId}`), Markup.button.callback(BUTTONS.DELETE, `${CALLBACK_PREFIXES.BIRTHDAY_DELETE}${birthdayId}`)],
      [Markup.button.callback(BUTTONS.DETAILS, `${CALLBACK_PREFIXES.BIRTHDAY_DETAILS}${birthdayId}`)],
      [Markup.button.callback(BUTTONS.BACK, CALLBACKS.BIRTHDAY_LIST)]
    ]);
  }

  static getSearchMenuKeyboard() {
    return Markup.inlineKeyboard([
      [Markup.button.callback(BUTTONS.WEB_SEARCH, CALLBACKS.SEARCH_WEB), Markup.button.callback(BUTTONS.SEARCH_TASKS, CALLBACKS.SEARCH_TASKS)],
      [Markup.button.callback(BUTTONS.SEARCH_BIRTHDAYS, CALLBACKS.SEARCH_BIRTHDAYS), Markup.button.callback(BUTTONS.SEARCH_EVENTS, CALLBACKS.SEARCH_EVENTS)],
      [Markup.button.callback(BUTTONS.SEARCH_ALL, CALLBACKS.SEARCH_ALL)],
      [Markup.button.callback(BUTTONS.BACK_TO_MAIN, CALLBACKS.MAIN_MENU)]
    ]);
  }

  static getPaginationKeyboard(currentPage: number, totalPages: number, baseAction: string) {
    const buttons = [];

    if (currentPage > 1) {
      buttons.push(Markup.button.callback("⬅️ Previous", `${baseAction}${CALLBACK_PREFIXES.PAGE}${currentPage - 1}`));
    }

    buttons.push(Markup.button.callback(`${currentPage}/${totalPages}`, CALLBACKS.NOOP));

    if (currentPage < totalPages) {
      buttons.push(Markup.button.callback("➡️ Next", `${baseAction}${CALLBACK_PREFIXES.PAGE}${currentPage + 1}`));
    }

    return Markup.inlineKeyboard([
      buttons,
      [Markup.button.callback(BUTTONS.BACK, `${baseAction.split("_")[0]}_menu`)]
    ]);
  }
}
