import { BaseRepository } from "./base.repository";
import type { Task } from "@/types";
import { DrizzleDB, tasks } from "../database/connection";
import { eq, desc, and, or, like, lt, between } from "drizzle-orm";

export class TaskRepository extends BaseRepository<Task> {
  constructor(db: DrizzleDB) {
    super(db);
  }

  // Find task by ID
  async findById(id: string): Promise<Task | null> {
    try {
      const result = await this.db
        .select()
        .from(tasks)
        .where(eq(tasks.id, id))
        .limit(1);

      return result.length > 0 ? this.mapFromDb(result[0]) : null;
    } catch (error) {
      console.error("Error finding task by id:", error);
      throw error;
    }
  }

  // Create a new task
  async create(data: Partial<Task>): Promise<Task> {
    try {
      const mappedData = this.mapToDb(data);

      await this.db
        .insert(tasks)
        .values(mappedData as any);

      const created = await this.findById(mappedData.id);
      if (!created) {
        throw new Error("Failed to retrieve created task");
      }

      return created;
    } catch (error) {
      console.error("Error creating task:", error);
      throw error;
    }
  }

  // Find tasks by user ID
  async findByUserId(userId: string, limit: number = 50, offset: number = 0): Promise<Task[]> {
    try {
      const results = await this.db
        .select()
        .from(tasks)
        .where(eq(tasks.userId, userId))
        .orderBy(desc(tasks.createdAt))
        .limit(limit)
        .offset(offset);

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding tasks by user ID:", error);
      throw error;
    }
  }

  // Find tasks by status
  async findByStatus(status: Task["status"], userId?: string): Promise<Task[]> {
    try {
      let whereCondition = eq(tasks.status, status);

      if (userId) {
        whereCondition = and(whereCondition, eq(tasks.userId, userId))!;
      }

      const results = await this.db
        .select()
        .from(tasks)
        .where(whereCondition)
        .orderBy(desc(tasks.createdAt));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding tasks by status:", error);
      throw error;
    }
  }

  // Find tasks with upcoming due dates
  async findUpcomingTasks(userId: string, days: number = 7): Promise<Task[]> {
    try {
      const now = new Date();
      const futureDate = new Date();
      futureDate.setDate(now.getDate() + days);

      const results = await this.db
        .select()
        .from(tasks)
        .where(
          and(
            eq(tasks.userId, userId),
            between(tasks.dueDate, now.toISOString(), futureDate.toISOString()),
            or(eq(tasks.status, "pending"), eq(tasks.status, "in-progress"))
          )!
        )
        .orderBy(tasks.dueDate);

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding upcoming tasks:", error);
      throw error;
    }
  }

  // Find overdue tasks
  async findOverdueTasks(userId?: string): Promise<Task[]> {
    try {
      const now = new Date().toISOString();
      let whereCondition = and(
        lt(tasks.dueDate, now),
        or(eq(tasks.status, "pending"), eq(tasks.status, "in-progress"))
      )!;

      if (userId) {
        whereCondition = and(whereCondition, eq(tasks.userId, userId))!;
      }

      const results = await this.db
        .select()
        .from(tasks)
        .where(whereCondition)
        .orderBy(tasks.dueDate);

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding overdue tasks:", error);
      throw error;
    }
  }

  // Search tasks by title or description
  async searchTasks(userId: string, searchTerm: string): Promise<Task[]> {
    try {
      const searchPattern = `%${searchTerm}%`;

      const results = await this.db
        .select()
        .from(tasks)
        .where(
          and(
            eq(tasks.userId, userId),
            or(
              like(tasks.title, searchPattern),
              like(tasks.description, searchPattern)
            )
          )!
        )
        .orderBy(desc(tasks.createdAt));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error searching tasks:", error);
      throw error;
    }
  }

  // Update task status
  async updateStatus(id: string, status: Task["status"]): Promise<Task | null> {
    try {
      const updateData: any = {
        status,
        updatedAt: this.getCurrentTimestamp()
      };

      if (status === "completed") {
        updateData.completedAt = this.getCurrentTimestamp();
      }

      await this.db
        .update(tasks)
        .set(updateData)
        .where(eq(tasks.id, id));

      return await this.findById(id);
    } catch (error) {
      console.error("Error updating task status:", error);
      throw error;
    }
  }

  // Update task with partial data
  async updateTask(id: string, updateData: Partial<Omit<Task, "id" | "userId" | "createdAt">>): Promise<Task | null> {
    try {
      const sanitizedData: any = {
        ...updateData,
        updatedAt: this.getCurrentTimestamp()
      };

      // Handle metadata updates by merging with existing metadata
      if (updateData.metadata) {
        const existingTask = await this.findById(id);
        if (existingTask) {
          sanitizedData.metadata = {
            ...existingTask.metadata,
            ...updateData.metadata
          };
        }
      }

      const result = await this.db
        .update(tasks)
        .set(sanitizedData)
        .where(eq(tasks.id, id))
        .returning();

      const task = result[0];
      if (!task) {
        return null;
      }

      return {
        ...task,
        description: task.description || undefined,
        dueDate: task.dueDate ? new Date(task.dueDate).toISOString() : undefined,
        reminderDate: task.reminderDate ? new Date(task.reminderDate).toISOString() : undefined,
        completedAt: task.completedAt ? new Date(task.completedAt).toISOString() : undefined,
        metadata: task.metadata ? JSON.parse(task.metadata) : { source: "direct" as const },
        attachments: task.attachments ? JSON.parse(task.attachments) : undefined,
        createdAt: new Date(task.createdAt).toISOString(),
        updatedAt: new Date(task.updatedAt).toISOString()
      };
    } catch (error) {
      console.error("Error updating task:", error);
      throw error;
    }
  }

  // Soft delete task (mark as deleted)
  async softDelete(id: string): Promise<boolean> {
    try {
      const existingTask = await this.findById(id);
      if (!existingTask) {
        return false;
      }

      await this.updateTask(id, {
        metadata: {
          source: existingTask.metadata?.source || "direct",
          ...existingTask.metadata,
          deleted: true,
          deletedAt: new Date().toISOString()
        }
      });

      return true;
    } catch (error) {
      console.error("Error soft deleting task:", error);
      return false;
    }
  }

  protected mapFromDb(row: any): Task {
    return {
      id: row.id,
      userId: row.userId,
      title: row.title,
      description: row.description || undefined,
      status: row.status,
      priority: row.priority,
      dueDate: row.dueDate ? new Date(row.dueDate).toISOString() : undefined,
      reminderDate: row.reminderDate ? new Date(row.reminderDate).toISOString() : undefined,
      createdAt: new Date(row.createdAt).toISOString(),
      updatedAt: new Date(row.updatedAt).toISOString(),
      completedAt: row.completedAt ? new Date(row.completedAt).toISOString() : undefined,
      metadata: row.metadata ? JSON.parse(row.metadata) : { source: "direct" },
      attachments: row.attachments ? JSON.parse(row.attachments) : undefined
    };
  }

  protected mapToDb(data: Partial<Task>): Record<string, any> {
    const mapped: Record<string, any> = {};

    if (data.id !== undefined) {
      mapped.id = data.id;
    }
    if (data.userId !== undefined) {
      mapped.userId = data.userId;
    }
    if (data.title !== undefined) {
      mapped.title = data.title;
    }
    if (data.description !== undefined) {
      mapped.description = data.description;
    }
    if (data.status !== undefined) {
      mapped.status = data.status;
    }
    if (data.priority !== undefined) {
      mapped.priority = data.priority;
    }
    if (data.dueDate !== undefined) {
      mapped.dueDate = data.dueDate;
    }
    if (data.reminderDate !== undefined) {
      mapped.reminderDate = data.reminderDate;
    }
    if (data.createdAt !== undefined) {
      mapped.createdAt = data.createdAt;
    }
    if (data.updatedAt !== undefined) {
      mapped.updatedAt = data.updatedAt;
    }
    if (data.completedAt !== undefined) {
      mapped.completedAt = data.completedAt;
    }
    if (data.metadata !== undefined) {
      mapped.metadata = JSON.stringify(data.metadata);
    }
    if (data.attachments !== undefined) {
      mapped.attachments = JSON.stringify(data.attachments);
    }

    // Set defaults for new tasks
    if (!mapped.id) {
      mapped.id = this.generateId();
    }
    if (!mapped.status) {
      mapped.status = "pending";
    }
    if (!mapped.priority) {
      mapped.priority = "medium";
    }
    if (!mapped.createdAt) {
      mapped.createdAt = this.getCurrentTimestamp();
    }
    if (!mapped.updatedAt) {
      mapped.updatedAt = this.getCurrentTimestamp();
    }
    if (!mapped.metadata) {
      mapped.metadata = JSON.stringify({ source: "direct" });
    }

    return mapped;
  }
}
