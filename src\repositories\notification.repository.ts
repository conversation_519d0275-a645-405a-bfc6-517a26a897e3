import { BaseRepository } from "./base.repository";
import type {
  Notification,
  NotificationType
} from "@/types";
import { DrizzleDB, notifications } from "../database/connection";
import { eq, and, lte, desc, asc, lt } from "drizzle-orm";

export class NotificationRepository extends BaseRepository<Notification> {
  constructor(db: DrizzleDB) {
    super(db);
  }

  // Create a new notification
  async create(data: Partial<Notification>): Promise<Notification> {
    try {
      const mappedData = this.mapToDb(data);

      await this.db.insert(notifications).values(mappedData);

      const result = await this.db
        .select()
        .from(notifications)
        .where(eq(notifications.id, mappedData.id))
        .limit(1);

      return this.mapFromDb(result[0]);
    } catch (error) {
      console.error("Error creating notification:", error);
      throw error;
    }
  }

  // Find notification by ID
  async findById(id: string): Promise<Notification | null> {
    try {
      const result = await this.db
        .select()
        .from(notifications)
        .where(eq(notifications.id, id))
        .limit(1);

      return result.length > 0 ? this.mapFromDb(result[0]) : null;
    } catch (error) {
      console.error("Error finding notification by ID:", error);
      throw error;
    }
  }

  // Find notifications by user ID with optional status filter
  async findByUserId(
    userId: string,
    status?: Notification["status"],
    limit: number = 50,
    offset: number = 0
  ): Promise<Notification[]> {
    try {
      const whereConditions = status
        ? and(eq(notifications.userId, userId), eq(notifications.status, status))!
        : eq(notifications.userId, userId);

      const results = await this.db
        .select()
        .from(notifications)
        .where(whereConditions)
        .orderBy(desc(notifications.scheduledFor))
        .limit(limit)
        .offset(offset);

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding notifications by user ID:", error);
      throw error;
    }
  }

  // Find notifications by status
  async findByStatus(status: Notification["status"]): Promise<Notification[]> {
    try {
      const results = await this.db
        .select()
        .from(notifications)
        .where(eq(notifications.status, status))
        .orderBy(asc(notifications.scheduledFor));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding notifications by status:", error);
      throw error;
    }
  }

  // Find pending notifications that should be sent now
  async findPendingNotifications(): Promise<Notification[]> {
    try {
      const now = new Date().toISOString();
      const results = await this.db
        .select()
        .from(notifications)
        .where(
          and(
            eq(notifications.status, "pending"),
            lte(notifications.scheduledFor, now)
          )!
        )
        .orderBy(asc(notifications.scheduledFor));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding pending notifications:", error);
      throw error;
    }
  }

  // Find notifications by type and entity ID
  async findByTypeAndEntity(type: NotificationType, entityId: string): Promise<Notification[]> {
    try {
      const results = await this.db
        .select()
        .from(notifications)
        .where(
          and(
            eq(notifications.type, type),
            eq(notifications.entityId, entityId)
          )!
        )
        .orderBy(desc(notifications.scheduledFor));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding notifications by type and entity:", error);
      throw error;
    }
  }

  // Update notification
  async update(id: string, data: Partial<Notification>): Promise<Notification | null> {
    try {
      const mappedData = this.mapToDb(data);

      await this.db
        .update(notifications)
        .set(mappedData)
        .where(eq(notifications.id, id));

      return this.findById(id);
    } catch (error) {
      console.error("Error updating notification:", error);
      throw error;
    }
  }

  // Delete notification
  async delete(id: string): Promise<boolean> {
    try {
      await this.db
        .delete(notifications)
        .where(eq(notifications.id, id));

      return true;
    } catch (error) {
      console.error("Error deleting notification:", error);
      throw error;
    }
  }

  // Mark notification as sent
  async markAsSent(id: string): Promise<Notification | null> {
    try {
      await this.db
        .update(notifications)
        .set({
          status: "sent",
          sentAt: this.getCurrentTimestamp()
        })
        .where(eq(notifications.id, id));

      return await this.findById(id);
    } catch (error) {
      console.error("Error marking notification as sent:", error);
      throw error;
    }
  }

  // Mark notification as failed
  async markAsFailed(id: string): Promise<Notification | null> {
    try {
      await this.db
        .update(notifications)
        .set({ status: "failed" })
        .where(eq(notifications.id, id));

      return await this.findById(id);
    } catch (error) {
      console.error("Error marking notification as failed:", error);
      throw error;
    }
  }

  // Update notification status
  async updateStatus(id: string, status: Notification["status"]): Promise<Notification | null> {
    try {
      const updateData: any = { status };

      if (status === "sent") {
        updateData.sentAt = this.getCurrentTimestamp();
      }

      await this.db
        .update(notifications)
        .set(updateData)
        .where(eq(notifications.id, id));

      return await this.findById(id);
    } catch (error) {
      console.error("Error updating notification status:", error);
      throw error;
    }
  }

  // Schedule a new notification
  async scheduleNotification(
    userId: string,
    type: Notification["type"],
    entityId: string,
    scheduledFor: Date,
    message: string
  ): Promise<Notification> {
    try {
      const notificationData: Partial<Notification> = {
        userId,
        type,
        entityId,
        scheduledFor: scheduledFor.toISOString(),
        message,
        status: "pending"
      };

      return await this.create(notificationData);
    } catch (error) {
      console.error("Error scheduling notification:", error);
      throw error;
    }
  }

  protected mapFromDb(row: any): Notification {
    return {
      id: row.id,
      userId: row.userId,
      type: row.type,
      entityId: row.entityId,
      scheduledFor: new Date(row.scheduledFor).toISOString(),
      sentAt: row.sentAt ? new Date(row.sentAt).toISOString() : undefined,
      status: row.status,
      message: row.message,
      createdAt: new Date(row.createdAt).toISOString(),
      priority: row.priority,
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined
    };
  }

  protected mapToDb(data: Partial<Notification>): any {
    const mapped: any = {};

    if (data.id !== undefined) {
      mapped.id = data.id;
    }
    if (data.userId !== undefined) {
      mapped.userId = data.userId;
    }
    if (data.type !== undefined) {
      mapped.type = data.type;
    }
    if (data.entityId !== undefined) {
      mapped.entityId = data.entityId;
    }
    if (data.scheduledFor !== undefined) {
      mapped.scheduledFor = data.scheduledFor;
    } // ✅ Convert Date to string
    if (data.sentAt !== undefined) {
      mapped.sentAt = data.sentAt;
    } // ✅ Convert Date to string
    if (data.status !== undefined) {
      mapped.status = data.status;
    }
    if (data.message !== undefined) {
      mapped.message = data.message;
    }
    if (data.createdAt !== undefined) {
      mapped.createdAt = data.createdAt;
    } // ✅ Convert Date to string
    if (data.priority !== undefined) {
      mapped.priority = data.priority;
    }
    if (data.metadata !== undefined) {
      mapped.metadata = JSON.stringify(data.metadata);
    }

    // Set defaults for new notifications
    if (!mapped.id) {
      mapped.id = this.generateId();
    }
    if (!mapped.status) {
      mapped.status = "pending";
    }
    if (!mapped.createdAt) {
      mapped.createdAt = this.getCurrentTimestamp();
    }

    return mapped;
  }

  // Find notifications older than a specific date
  async findOlderThan(date: Date, status?: Notification["status"]): Promise<Notification[]> {
    try {
      const dateStr = date.toISOString();

      const whereConditions = [lt(notifications.createdAt, dateStr)];
      if (status) {
        whereConditions.push(eq(notifications.status, status));
      }

      const results = await this.db
        .select()
        .from(notifications)
        .where(and(...whereConditions))
        .execute();

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding old notifications:", error);
      throw error;
    }
  }
}
