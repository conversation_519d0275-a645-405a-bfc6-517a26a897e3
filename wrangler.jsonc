/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "novers-telegram-bot",
  "main": "src/index.ts",
  "compatibility_flags": ["nodejs_compat"],
  "compatibility_date": "2025-08-02",
  "assets": {
    "binding": "ASSETS",
    "directory": "./public"
  },
  "observability": {
    "enabled": true
  },

  /**
   * Environment Variables
   * Note: Use secrets to store sensitive data like TELEGRAM_BOT_TOKEN
   */
  "vars": {
    "ENVIRONMENT": "development"
  },

  /**
   * Bindings for Cloudflare services
   */
  "kv_namespaces": [
    {
      "binding": "BOT_KV",
      "id": "placeholder_kv_id",
      "preview_id": "placeholder_kv_preview_id"
    }
  ],

  "d1_databases": [
    {
      "binding": "BOT_DB",
      "database_name": "novers_telegram_bot",
      "database_id": "placeholder_db_id"
    }
  ],

  /**
   * Enable Cloudflare AI
   */
  "ai": {
    "binding": "AI"
  },

  /**
   * Cron Triggers for scheduled tasks
   */
  "triggers": {
    "crons": [
      "*/5 * * * *", // Every 5 minutes - main notification processing
      "0 9 * * *", // Daily at 9 AM - birthday and event scheduling
      "0 0 * * 0" // Weekly on Sunday at midnight - cleanup
    ]
  }
}
