// Production-scale types and interfaces for Telegraf bot

import { Context, Scenes } from "telegraf";
import { USER_QUOTAS, FEATURES } from "../constants/config";

// User tier management for monetization
export interface UserTier {
  type: "admin" | "free" | "paid";
  features: string[];
  quotas: UserQuotas;
  subscriptionId?: string;
  subscriptionExpiry?: Date;
  upgradePromptCount?: number;
  lastUpgradePrompt?: Date;
}

// Usage quotas for different user tiers
export interface UserQuotas {
  aiRequests: { used: number; limit: number; resetDate: Date };
  tasks: { used: number; limit: number };
  birthdays: { used: number; limit: number };
  notifications: { used: number; limit: number };
  dailyActions: { used: number; limit: number; resetDate: Date };
  exports: { used: number; limit: number; resetDate: Date };
}

// Rate limiting data
export interface RateLimitData {
  lastRequest: Date;
  requestCount: number;
  windowStart: Date;
  isBlocked: boolean;
  blockUntil?: Date;
  warningCount: number;
}

// User preferences
export interface UserPreferences {
  timezone: string;
  language: string;
  notificationTime: string;
  quietHours: { start: string; end: string };
  aiEnhancement: boolean;
  theme: "light" | "dark";
  compactMode: boolean;
  soundEnabled: boolean;
}

// Analytics data
export interface UserAnalytics {
  totalCommands: number;
  totalMessages: number;
  featuresUsed: string[];
  lastFeatureUsed?: string;
  sessionDuration: number;
  averageSessionDuration: number;
}

// Production session data extending WizardSessionData
export interface TWizardSession extends Scenes.WizardSessionData {
  // Current operation context
  currentOperation?: {
    type: "task_creation" | "birthday_creation" | "event_creation" | "subscription" | "search";
    step: number;
    data: Record<string, any>;
    startedAt: Date;
    expiresAt: Date;
  };

  // Temporary data for operations
  tempData?: Record<string, any>;
}

// Production session wrapper
export interface TSession extends Scenes.WizardSession<TWizardSession> {
  // User management data
  user?: {
    id: string;
    username?: string;
    firstName: string;
    lastName?: string;
    tier: UserTier;
    preferences: UserPreferences;
    analytics: UserAnalytics;
    createdAt: Date;
    lastActive: Date;
    isBlocked?: boolean;
    blockReason?: string;
  };

  // Rate limiting data
  rateLimit?: RateLimitData;
}

// Enhanced bot context with scenes support (Option 3: Extend global context and global session)
export interface TBotContext extends Context {
  // Global session with production data
  session: TSession;

  // Scene management (will be added by Telegraf middleware)
  scene: Scenes.SceneContextScene<TBotContext, TWizardSession>;
  wizard: Scenes.WizardContextWizard<TBotContext>;
}


// Error types for production error handling
export enum BotErrorType {
  RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
  QUOTA_EXCEEDED = "QUOTA_EXCEEDED",
  INVALID_INPUT = "INVALID_INPUT",
  SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
  UNAUTHORIZED = "UNAUTHORIZED",
  PAYMENT_REQUIRED = "PAYMENT_REQUIRED",
  INTERNAL_ERROR = "INTERNAL_ERROR"
}

export interface BotError {
  type: BotErrorType;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
  userMessage: string;
}
