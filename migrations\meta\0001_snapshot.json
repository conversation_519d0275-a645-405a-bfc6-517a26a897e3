{"version": "6", "dialect": "sqlite", "id": "fa22625a-48aa-4da2-a256-bc9304f09bfa", "prevId": "007031db-0c6c-4dd6-a4a5-e1e655285f4a", "tables": {"attachments": {"name": "attachments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "taskId": {"name": "taskId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileId": {"name": "fileId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileName": {"name": "fileName", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mimeType": {"name": "mimeType", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "fileSize": {"name": "fileSize", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "uploadedAt": {"name": "uploadedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_attachments_taskId": {"name": "idx_attachments_taskId", "columns": ["taskId"], "isUnique": false}, "idx_attachments_type": {"name": "idx_attachments_type", "columns": ["type"], "isUnique": false}}, "foreignKeys": {"attachments_taskId_tasks_id_fk": {"name": "attachments_taskId_tasks_id_fk", "tableFrom": "attachments", "tableTo": "tasks", "columnsFrom": ["taskId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "birthdays": {"name": "birthdays", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "day": {"name": "day", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "month": {"name": "month", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "reminderDays": {"name": "reminderDays", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[1,7]'"}, "suggestions": {"name": "suggestions", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {"idx_birthdays_userId": {"name": "idx_birthdays_userId", "columns": ["userId"], "isUnique": false}, "idx_birthdays_date": {"name": "idx_birthdays_date", "columns": ["month", "day"], "isUnique": false}, "idx_birthdays_active": {"name": "idx_birthdays_active", "columns": ["active"], "isUnique": false}, "idx_birthdays_user_active": {"name": "idx_birthdays_user_active", "columns": ["userId", "active"], "isUnique": false}}, "foreignKeys": {"birthdays_userId_users_id_fk": {"name": "birthdays_userId_users_id_fk", "tableFrom": "birthdays", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "christian_events": {"name": "christian_events", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fixedDate": {"name": "fixedDate", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "calculation": {"name": "calculation", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reminderDays": {"name": "reminderDays", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[1,7]'"}, "recommendations": {"name": "recommendations", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {"christian_events_name_unique": {"name": "christian_events_name_unique", "columns": ["name"], "isUnique": true}, "idx_events_type": {"name": "idx_events_type", "columns": ["type"], "isUnique": false}, "idx_events_active": {"name": "idx_events_active", "columns": ["active"], "isUnique": false}, "idx_events_name": {"name": "idx_events_name", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "notifications": {"name": "notifications", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "entityId": {"name": "entityId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "scheduledFor": {"name": "scheduledFor", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "sentAt": {"name": "sentAt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'medium'"}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_notifications_userId": {"name": "idx_notifications_userId", "columns": ["userId"], "isUnique": false}, "idx_notifications_status": {"name": "idx_notifications_status", "columns": ["status"], "isUnique": false}, "idx_notifications_scheduledFor": {"name": "idx_notifications_scheduledFor", "columns": ["scheduledFor"], "isUnique": false}, "idx_notifications_type": {"name": "idx_notifications_type", "columns": ["type"], "isUnique": false}, "idx_notifications_entityId": {"name": "idx_notifications_entityId", "columns": ["entityId"], "isUnique": false}, "idx_notifications_priority": {"name": "idx_notifications_priority", "columns": ["priority"], "isUnique": false}, "idx_notifications_pending": {"name": "idx_notifications_pending", "columns": ["status", "scheduledFor"], "isUnique": false}}, "foreignKeys": {"notifications_userId_users_id_fk": {"name": "notifications_userId_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tasks": {"name": "tasks", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'medium'"}, "dueDate": {"name": "dueDate", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reminderDate": {"name": "reminderDate", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "completedAt": {"name": "completedAt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'{\"source\":\"direct\"}'"}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"idx_tasks_userId": {"name": "idx_tasks_userId", "columns": ["userId"], "isUnique": false}, "idx_tasks_status": {"name": "idx_tasks_status", "columns": ["status"], "isUnique": false}, "idx_tasks_dueDate": {"name": "idx_tasks_dueDate", "columns": ["dueDate"], "isUnique": false}, "idx_tasks_priority": {"name": "idx_tasks_priority", "columns": ["priority"], "isUnique": false}, "idx_tasks_createdAt": {"name": "idx_tasks_createdAt", "columns": ["createdAt"], "isUnique": false}, "idx_tasks_user_status": {"name": "idx_tasks_user_status", "columns": ["userId", "status"], "isUnique": false}}, "foreignKeys": {"tasks_userId_users_id_fk": {"name": "tasks_userId_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "lastActive": {"name": "lastActive", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "preferences": {"name": "preferences", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'{\"timezone\":\"UTC\",\"notificationTime\":\"09:00\",\"mode\":\"chat\"}'"}}, "indexes": {"idx_users_username": {"name": "idx_users_username", "columns": ["username"], "isUnique": false}, "idx_users_lastActive": {"name": "idx_users_lastActive", "columns": ["lastActive"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}