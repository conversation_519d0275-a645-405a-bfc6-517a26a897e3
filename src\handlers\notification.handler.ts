import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator as z<PERSON><PERSON><PERSON>tor } from "hono-openapi/zod";
import { RepositoryFactory } from "../repositories";
import { ErrorResponseSchema } from "../schemas/common.schema";
import {
  NotificationListResponseSchema,
  NotificationQuerySchema
} from "../schemas/notification.schema";
import { NotificationService } from "../services/notification.service";

const router = new Hono<{ Bindings: CloudflareBindings }>();

// GET /api/notifications - Get user notifications
router.get(
  "/",
  describeRoute({
    description: "Get user notifications with optional filtering",
    responses: {
      200: {
        description: "Successfully retrieved notifications",
        content: {
          "application/json": {
            schema: resolver(NotificationListResponseSchema.meta({
              example: {
                success: true,
                data: [{
                  id: "123e4567-e89b-12d3-a456-426614174000",
                  userId: "user123",
                  type: "task_reminder",
                  entityId: "task-123",
                  title: "Task Reminder",
                  message: "Don't forget to complete your task",
                  status: "pending",
                  scheduledFor: "2024-01-15T10:00:00Z",
                  createdAt: "2024-01-15T09:00:00Z"
                }],
                pagination: {
                  total: 1,
                  limit: 20,
                  offset: 0,
                  hasMore: false
                }
              }
            }))
          }
        }
      },
      400: {
        description: "Bad request - User ID required",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("query", NotificationQuerySchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const query = c.req.valid("query");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const notificationService = new NotificationService(repositoryFactory);

      const notifications = await notificationService.getUserNotifications(
        userId,
        query.status,
        query.limit || 20
      );

      return c.json({
        success: true,
        data: notifications,
        pagination: {
          total: notifications.length,
          limit: query.limit || 20,
          offset: query.offset || 0,
          hasMore: notifications.length === (query.limit || 20)
        }
      });
    } catch (error: any) {
      console.error("Get notifications error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

// POST /api/notifications - Create new notification
router.post("/", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const body = await c.req.json();
    const { type, entityId, scheduledFor, message, options } = body;

    if (!type || !entityId || !scheduledFor || !message) {
      return c.json({ error: "Type, entityId, scheduledFor, and message are required" }, 400);
    }

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const notificationService = new NotificationService(repositoryFactory);

    const notification = await notificationService.createNotification(
      userId,
      type,
      entityId,
      new Date(scheduledFor),
      message,
      options
    );

    return c.json({
      success: true,
      data: notification
    }, 201);
  } catch (error: any) {
    console.error("Create notification error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// GET /api/notifications/stats - Get notification statistics
router.get("/stats", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const notificationService = new NotificationService(repositoryFactory);

    const [pending, sent, failed] = await Promise.all([
      notificationService.getUserNotifications(userId, "pending", 100),
      notificationService.getUserNotifications(userId, "sent", 100),
      notificationService.getUserNotifications(userId, "failed", 100)
    ]);

    return c.json({
      success: true,
      data: {
        pending: pending.length,
        sent: sent.length,
        failed: failed.length,
        total: pending.length + sent.length + failed.length
      }
    });
  } catch (error: any) {
    console.error("Get notification stats error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// PUT /api/notifications/:id - Update notification
router.put("/:id", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    const notificationId = c.req.param("id");

    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const body = await c.req.json();
    const updates = body;

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const notificationService = new NotificationService(repositoryFactory);

    const notification = await notificationService.updateNotification(notificationId, userId, updates);

    return c.json({
      success: true,
      data: notification
    });
  } catch (error: any) {
    console.error("Update notification error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// DELETE /api/notifications/:id - Delete notification
router.delete("/:id", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    const notificationId = c.req.param("id");

    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const notificationService = new NotificationService(repositoryFactory);

    await notificationService.deleteNotification(notificationId, userId);

    return c.json({
      success: true,
      message: "Notification deleted successfully"
    });
  } catch (error: any) {
    console.error("Delete notification error:", error);
    return c.json({ error: error.message }, 500);
  }
});

export default router;
