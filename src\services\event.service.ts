// Christian events service for managing Christian calendar events and reminders

import { RepositoryFactory } from "../repositories";
import type { ChristianEvent, CalculationMethod } from "@/types";

export class EventService {
  private repositoryFactory: RepositoryFactory;

  constructor(repositoryFactory: RepositoryFactory) {
    this.repositoryFactory = repositoryFactory;
  }

  /**
   * Add a new Christian event (global events, not user-specific)
   */
  async addEvent(
    name: string,
    type: "fixed" | "calculated",
    fixedDate?: { day: number; month: number },
    calculation?: CalculationMethod,
    reminderDays: number[] = [1, 7, 30]
  ): Promise<ChristianEvent> {
    const eventRepo = this.repositoryFactory.getEventRepository();

    // Validate input
    if (!name.trim()) {
      throw new Error("Event name is required");
    }

    if (type === "fixed" && !fixedDate) {
      throw new Error("Fixed date is required for fixed events");
    }

    if (type === "calculated" && !calculation) {
      throw new Error("Calculation method is required for calculated events");
    }

    // Check if event already exists with this name
    const existingEvents = await eventRepo.findAll();
    const duplicate = existingEvents.find((e: ChristianEvent) =>
      e.name.toLowerCase() === name.toLowerCase()
    );

    if (duplicate) {
      throw new Error(`Event "${name}" already exists`);
    }

    const event = await eventRepo.create({
      name: name.trim(),
      type,
      fixedDate,
      calculation,
      reminderDays,
      isActive: true
    });

    // Schedule notifications for this event
    await this.scheduleNotifications(event);

    return event;
  }

  /**
   * Get all Christian events (global events, not user-specific)
   */
  async getAllEvents(): Promise<ChristianEvent[]> {
    const eventRepo = this.repositoryFactory.getEventRepository();
    return eventRepo.findAll();
  }

  /**
   * Get upcoming Christian events within specified days
   */
  async getUpcomingEvents(days: number = 30): Promise<Array<ChristianEvent & { eventDate: Date; daysUntilEvent: number }>> {
    const eventRepo = this.repositoryFactory.getEventRepository();
    const userEvents = await eventRepo.findUpcoming(days);

    const today = new Date();
    const endDate = new Date(today);
    endDate.setDate(today.getDate() + days);

    const upcomingEvents: Array<ChristianEvent & { eventDate: Date; daysUntilEvent: number }> = [];

    for (const event of userEvents) {
      if (!event.isActive) {
        continue;
      }

      const eventDate = this.calculateEventDate(event, today.getFullYear());

      // If this year's event has passed, get next year's
      let finalEventDate = eventDate;
      if (eventDate && eventDate < today) {
        finalEventDate = this.calculateEventDate(event, today.getFullYear() + 1);
      }

      if (finalEventDate && finalEventDate >= today && finalEventDate <= endDate) {
        upcomingEvents.push({
          ...event,
          eventDate: finalEventDate,
          daysUntilEvent: Math.ceil((finalEventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
        });
      }
    }

    // Sort by event date
    return upcomingEvents.sort((a, b) =>
      a.eventDate.getTime() - b.eventDate.getTime()
    );
  }

  /**
   * Get a specific event
   */
  async getEvent(eventId: string): Promise<ChristianEvent | null> {
    const eventRepo = this.repositoryFactory.getEventRepository();
    const event = await eventRepo.findById(eventId);

    if (!event) {
      return null;
    }

    return event;
  }

  /**
   * Update an event
   */
  async updateEvent(
    eventId: string,
    updates: Partial<ChristianEvent>
  ): Promise<ChristianEvent | null> {
    const eventRepo = this.repositoryFactory.getEventRepository();
    const existing = await this.getEvent(eventId);

    if (!existing) {
      throw new Error("Event not found");
    }

    // Validate updates
    if (updates.type === "fixed" && !updates.fixedDate) {
      throw new Error("Fixed date is required for fixed events");
    }

    if (updates.type === "calculated" && !updates.calculation) {
      throw new Error("Calculation method is required for calculated events");
    }

    const updated = await eventRepo.update(eventId, updates);

    if (!updated) {
      return null;
    }

    // Reschedule notifications if reminder settings changed
    if (updates.reminderDays || updates.type || updates.fixedDate || updates.calculation) {
      await this.scheduleNotifications(updated);
    }

    return updated;
  }

  /**
   * Delete an event
   */
  async deleteEvent(eventId: string): Promise<void> {
    const eventRepo = this.repositoryFactory.getEventRepository();
    const existing = await this.getEvent(eventId);

    if (!existing) {
      throw new Error("Event not found");
    }

    await eventRepo.delete(eventId);

    // Cancel any pending notifications for this event
    await this.cancelNotifications(eventId);
  }

  /**
   * Calculate the actual date for an event in a given year
   */
  calculateEventDate(event: ChristianEvent, year: number): Date | null {
    if (event.type === "fixed" && event.fixedDate) {
      return new Date(year, event.fixedDate.month - 1, event.fixedDate.day);
    }

    if (event.type === "calculated" && event.calculation) {
      const easterDate = this.calculateEaster(year);

      if (event.calculation === "easter") {
        const eventDate = new Date(easterDate);
        eventDate.setDate(easterDate.getDate());
        return eventDate;
      }
    }

    return null;
  }


  /**
   * Calculate Easter date for a given year using the algorithm
   */
  private calculateEaster(year: number): Date {
    // Using the algorithm for Western Christianity (Gregorian calendar)
    const a = year % 19;
    const b = Math.floor(year / 100);
    const c = year % 100;
    const d = Math.floor(b / 4);
    const e = b % 4;
    const f = Math.floor((b + 8) / 25);
    const g = Math.floor((b - f + 1) / 3);
    const h = (19 * a + b - d - g + 15) % 30;
    const i = Math.floor(c / 4);
    const k = c % 4;
    const l = (32 + 2 * e + 2 * i - h - k) % 7;
    const m = Math.floor((a + 11 * h + 22 * l) / 451);
    const month = Math.floor((h + l - 7 * m + 114) / 31);
    const day = ((h + l - 7 * m + 114) % 31) + 1;

    return new Date(year, month - 1, day);
  }

  /**
   * Schedule notifications for an event
   */
  private async scheduleNotifications(event: ChristianEvent): Promise<void> {
    // For now, just log that we would schedule notifications
    // This will be implemented when we integrate with the notification system
    const eventDate = this.calculateEventDate(event, new Date().getFullYear());
    console.log(`Scheduling notifications for ${event.name} event on ${eventDate?.toDateString()}`);
  }

  /**
   * Cancel notifications for an event
   */
  private async cancelNotifications(eventId: string): Promise<void> {
    // For now, just log that we would cancel notifications
    console.log(`Canceling notifications for event ${eventId}`);
  }

  /**
   * Get event statistics (global events)
   */
  async getEventStats(): Promise<{
    total: number;
    thisMonth: number;
    upcoming30Days: number;
    byType: Record<string, number>;
  }> {
    const events = await this.getAllEvents();
    const currentMonth = new Date().getMonth() + 1;
    const upcoming = await this.getUpcomingEvents(30);

    const byType = events.reduce((acc: Record<string, number>, event: ChristianEvent) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const thisMonth = events.filter((e: ChristianEvent) => {
      const eventDate = this.calculateEventDate(e, new Date().getFullYear());
      if (!eventDate) {
        return false;
      }
      return eventDate.getMonth() + 1 === currentMonth;
    }).length;

    return {
      total: events.filter((e: ChristianEvent) => e.isActive).length,
      thisMonth,
      upcoming30Days: upcoming.length,
      byType
    };
  }
}
