// Drizzle ORM schema definitions for Novers Telegram Bot Assistant

import { sqliteTable, text, integer, index } from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";

// Users table
export const users = sqliteTable("users", {
  id: text("id").primaryKey(),
  username: text("username"),
  firstName: text("firstName").notNull(),
  lastName: text("lastName"),
  createdAt: text("createdAt").notNull(),
  lastActive: text("lastActive").notNull(),
  preferences: text("preferences").notNull().default("{\"timezone\":\"UTC\",\"notificationTime\":\"09:00\",\"mode\":\"chat\"}")
}, (table) => ({
  usernameIdx: index("idx_users_username").on(table.username),
  lastActiveIdx: index("idx_users_lastActive").on(table.lastActive)
}));

// Tasks table
export const tasks = sqliteTable("tasks", {
  id: text("id").primary<PERSON>ey(),
  userId: text("userId").notNull().references(() => users.id, { onDelete: "cascade" }),
  title: text("title").notNull(),
  description: text("description"),
  status: text("status", { enum: ["pending", "in-progress", "completed", "cancelled"] }).notNull().default("pending"),
  priority: text("priority", { enum: ["low", "medium", "high"] }).notNull().default("medium"),
  dueDate: text("dueDate"),
  reminderDate: text("reminderDate"),
  createdAt: text("createdAt").notNull(),
  updatedAt: text("updatedAt").notNull(),
  completedAt: text("completedAt"),
  metadata: text("metadata").notNull().default("{\"source\":\"direct\"}"),
  attachments: text("attachments")
}, (table) => ({
  userIdIdx: index("idx_tasks_userId").on(table.userId),
  statusIdx: index("idx_tasks_status").on(table.status),
  dueDateIdx: index("idx_tasks_dueDate").on(table.dueDate),
  priorityIdx: index("idx_tasks_priority").on(table.priority),
  createdAtIdx: index("idx_tasks_createdAt").on(table.createdAt),
  userStatusIdx: index("idx_tasks_user_status").on(table.userId, table.status)
}));

// Birthdays table
export const birthdays = sqliteTable("birthdays", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => users.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  day: integer("day").notNull(),
  month: integer("month").notNull(),
  year: integer("year"),
  reminderDays: text("reminderDays").notNull().default("[1,7]"),
  suggestions: text("suggestions").notNull().default("[]"),
  createdAt: text("createdAt").notNull(),
  updatedAt: text("updatedAt").notNull(),
  active: integer("active", { mode: "boolean" }).notNull().default(true)
}, (table) => ({
  userIdIdx: index("idx_birthdays_userId").on(table.userId),
  dateIdx: index("idx_birthdays_date").on(table.month, table.day),
  activeIdx: index("idx_birthdays_active").on(table.active),
  userActiveIdx: index("idx_birthdays_user_active").on(table.userId, table.active)
}));

// Christian Events table
export const christianEvents = sqliteTable("christian_events", {
  id: text("id").primaryKey(),
  name: text("name").notNull().unique(),
  type: text("type", { enum: ["fixed", "calculated"] }).notNull(),
  fixedDate: text("fixedDate"), // JSON: {"day": 25, "month": 12}
  calculation: text("calculation"), // JSON: {"method": "easter", "offsetDays": 0}
  reminderDays: text("reminderDays").notNull().default("[1,7]"),
  recommendations: text("recommendations").notNull().default("[]"),
  description: text("description"),
  createdAt: text("createdAt").notNull(),
  updatedAt: text("updatedAt").notNull(),
  active: integer("active", { mode: "boolean" }).notNull().default(true)
}, (table) => ({
  typeIdx: index("idx_events_type").on(table.type),
  activeIdx: index("idx_events_active").on(table.active),
  nameIdx: index("idx_events_name").on(table.name)
}));

// Notifications table
export const notifications = sqliteTable("notifications", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => users.id, { onDelete: "cascade" }),
  type: text("type", {
    enum: ["task-deadline", "birthday", "christian-event", "task-reminder", "event-reminder", "custom"]
  }).notNull(),
  entityId: text("entityId").notNull(),
  scheduledFor: text("scheduledFor").notNull(),
  sentAt: text("sentAt"),
  status: text("status", {
    enum: ["pending", "sent", "failed", "cancelled", "snoozed"]
  }).notNull().default("pending"),
  message: text("message").notNull(),
  priority: text("priority", { enum: ["low", "medium", "high"] }).default("medium"),
  metadata: text("metadata"), // JSON string for additional data
  createdAt: text("createdAt").notNull()
}, (table) => ({
  userIdIdx: index("idx_notifications_userId").on(table.userId),
  statusIdx: index("idx_notifications_status").on(table.status),
  scheduledForIdx: index("idx_notifications_scheduledFor").on(table.scheduledFor),
  typeIdx: index("idx_notifications_type").on(table.type),
  entityIdIdx: index("idx_notifications_entityId").on(table.entityId),
  priorityIdx: index("idx_notifications_priority").on(table.priority),
  pendingIdx: index("idx_notifications_pending").on(table.status, table.scheduledFor)
}));

// Attachments table
export const attachments = sqliteTable("attachments", {
  id: text("id").primaryKey(),
  taskId: text("taskId").notNull().references(() => tasks.id, { onDelete: "cascade" }),
  type: text("type", { enum: ["document", "photo", "audio", "video"] }).notNull(),
  fileId: text("fileId").notNull(),
  fileName: text("fileName"),
  mimeType: text("mimeType"),
  fileSize: integer("fileSize"),
  uploadedAt: text("uploadedAt").notNull()
}, (table) => ({
  taskIdIdx: index("idx_attachments_taskId").on(table.taskId),
  typeIdx: index("idx_attachments_type").on(table.type)
}));

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  tasks: many(tasks),
  birthdays: many(birthdays),
  notifications: many(notifications)
}));

export const tasksRelations = relations(tasks, ({ one, many }) => ({
  user: one(users, {
    fields: [tasks.userId],
    references: [users.id]
  }),
  attachments: many(attachments)
}));

export const birthdaysRelations = relations(birthdays, ({ one }) => ({
  user: one(users, {
    fields: [birthdays.userId],
    references: [users.id]
  })
}));

export const notificationsRelations = relations(notifications, ({ one }) => ({
  user: one(users, {
    fields: [notifications.userId],
    references: [users.id]
  })
}));

export const attachmentsRelations = relations(attachments, ({ one }) => ({
  task: one(tasks, {
    fields: [attachments.taskId],
    references: [tasks.id]
  })
}));

// Export schema type
export type DatabaseSchema = {
  users: typeof users;
  tasks: typeof tasks;
  birthdays: typeof birthdays;
  christianEvents: typeof christianEvents;
  notifications: typeof notifications;
  attachments: typeof attachments;
};
