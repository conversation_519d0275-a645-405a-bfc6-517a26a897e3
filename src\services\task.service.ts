// Task service for managing task operations and business logic

import { RepositoryFactory } from "../repositories";
import type { Task, TaskPriority } from "@/types";
import { AIService, TaskExtractionResult, TaskEnhancementResult } from "../ai/ai.service";

export interface TaskCreationMetadata {
  source: "direct" | "forwarded" | "ai-generated";
  messageId?: number;
  attachments?: Array<{
    type: string;
    fileId: string;
    fileName?: string;
    mimeType?: string;
  }>;
  originalMessage?: string;
  extractedInfo?: Record<string, any>;
  useAI?: boolean; // Whether to use AI for task enhancement
  enhancementLevel?: "basic" | "full"; // Level of AI enhancement
}

export class TaskService {
  private repositoryFactory: RepositoryFactory;
  private aiService?: AIService;

  constructor(repositoryFactory: RepositoryFactory, aiService?: AIService) {
    this.repositoryFactory = repositoryFactory;
    this.aiService = aiService;
  }

  // Create a task from a message with optional AI enhancement
  async createTaskFromMessage(
    userId: string,
    messageText: string,
    metadata: TaskCreationMetadata
  ): Promise<Task> {
    const taskRepo = this.repositoryFactory.getTaskRepository();

    let taskInfo: any;
    let aiExtraction: TaskExtractionResult | null = null;
    let aiEnhancement: TaskEnhancementResult | null = null;

    // Use AI if available and requested
    if (this.aiService && metadata.useAI !== false) {
      try {
        console.log("🤖 Using AI to extract task information...");
        aiExtraction = await this.aiService.extractTaskInformation(messageText);

        // Enhance the task if full enhancement is requested
        if (metadata.enhancementLevel === "full") {
          console.log("🚀 Enhancing task with AI...");
          aiEnhancement = await this.aiService.enhanceTask(aiExtraction.title, aiExtraction.description);
        }

        // Use AI-extracted information
        taskInfo = {
          title: aiEnhancement?.enhancedTitle || aiExtraction.title,
          description: aiEnhancement?.enhancedDescription || aiExtraction.description,
          priority: aiExtraction.priority,
          dueDate: aiExtraction.dueDate,
          reminderDate: aiExtraction.reminderDate,
          extractedInfo: {
            aiProcessed: true,
            confidence: aiExtraction.confidence,
            category: aiExtraction.category,
            tags: aiExtraction.tags,
            estimatedDuration: aiExtraction.estimatedDuration,
            extractedEntities: aiExtraction.extractedEntities,
            suggestedSubtasks: aiEnhancement?.suggestedSubtasks,
            relatedTopics: aiEnhancement?.relatedTopics,
            improvementSuggestions: aiEnhancement?.improvementSuggestions,
            originalMessage: messageText
          }
        };

        console.log(`✅ AI extraction completed with ${(aiExtraction.confidence * 100).toFixed(1)}% confidence`);
      } catch (error) {
        console.error("❌ AI processing failed, falling back to basic extraction:", error);
        taskInfo = this.extractTaskInfo(messageText);
      }
    } else {
      // Use basic extraction
      taskInfo = this.extractTaskInfo(messageText);
    }

    const task = await taskRepo.create({
      id: this.generateId(),
      userId,
      title: taskInfo.title,
      description: taskInfo.description,
      status: "pending" as const,
      priority: taskInfo.priority,
      dueDate: taskInfo.dueDate ? new Date(taskInfo.dueDate).toISOString() : undefined,
      reminderDate: taskInfo.reminderDate ? new Date(taskInfo.reminderDate).toISOString() : undefined,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        source: metadata.source,
        originalMessage: metadata.originalMessage || messageText,
        messageId: metadata.messageId,
        extractedInfo: taskInfo.extractedInfo,
        aiProcessed: !!aiExtraction,
        enhancementLevel: metadata.enhancementLevel
      }
    });

    return task;
  }

  // Get user tasks with filtering
  async getUserTasks(
    userId: string,
    filter: "all" | "pending" | "in-progress" | "completed" | "overdue" = "all",
    limit: number = 50,
    offset: number = 0
  ): Promise<Task[]> {
    const taskRepo = this.repositoryFactory.getTaskRepository();

    switch (filter) {
    case "pending":
      return taskRepo.findByStatus("pending", userId);
    case "in-progress":
      return taskRepo.findByStatus("in-progress", userId);
    case "completed":
      return taskRepo.findByStatus("completed", userId);
    case "overdue":
      return taskRepo.findOverdueTasks(userId);
    default:
      return taskRepo.findByUserId(userId, limit, offset);
    }
  }

  // Update task status
  async updateTaskStatus(taskId: string, status: "pending" | "in-progress" | "completed" | "cancelled"): Promise<Task | null> {
    const taskRepo = this.repositoryFactory.getTaskRepository();
    return taskRepo.updateStatus(taskId, status);
  }

  // Search tasks
  async searchTasks(userId: string, searchTerm: string): Promise<Task[]> {
    const taskRepo = this.repositoryFactory.getTaskRepository();
    return taskRepo.searchTasks(userId, searchTerm);
  }

  // Get upcoming tasks
  async getUpcomingTasks(userId: string, days: number = 7): Promise<Task[]> {
    const taskRepo = this.repositoryFactory.getTaskRepository();
    return taskRepo.findUpcomingTasks(userId, days);
  }

  // Get overdue tasks
  async getOverdueTasks(userId: string): Promise<Task[]> {
    const taskRepo = this.repositoryFactory.getTaskRepository();
    return taskRepo.findOverdueTasks(userId);
  }

  // AI-powered task enhancement
  async enhanceExistingTask(taskId: string, userId: string): Promise<Task | null> {
    if (!this.aiService) {
      throw new Error("AI service not available");
    }

    const taskRepo = this.repositoryFactory.getTaskRepository();
    const task = await taskRepo.findById(taskId);

    if (!task || task.userId !== userId) {
      return null;
    }

    try {
      const enhancement = await this.aiService.enhanceTask(task.title, task.description);

      // Update task with AI enhancements
      const updatedTask = await taskRepo.updateTask(taskId, {
        title: enhancement.enhancedTitle,
        description: enhancement.enhancedDescription,
        metadata: {
          source: task.metadata?.source || "direct",
          ...task.metadata,
          aiEnhanced: true,
          enhancementDate: new Date().toISOString(),
          suggestedSubtasks: enhancement.suggestedSubtasks,
          relatedTopics: enhancement.relatedTopics,
          improvementSuggestions: enhancement.improvementSuggestions
        }
      });

      return updatedTask;
    } catch (error) {
      console.error("Failed to enhance task:", error);
      throw error;
    }
  }

  // AI-powered task categorization
  async categorizeTask(taskId: string, userId: string): Promise<string | null> {
    if (!this.aiService) {
      throw new Error("AI service not available");
    }

    const taskRepo = this.repositoryFactory.getTaskRepository();
    const task = await taskRepo.findById(taskId);

    if (!task || task.userId !== userId) {
      return null;
    }

    try {
      const category = await this.aiService.categorizeTask(task.title, task.description);

      // Update task with category
      await taskRepo.updateTask(taskId, {
        metadata: {
          source: task.metadata?.source || "direct",
          ...task.metadata,
          category,
          categorizedAt: new Date().toISOString()
        }
      });

      return category;
    } catch (error) {
      console.error("Failed to categorize task:", error);
      return null;
    }
  }

  // Bulk AI processing for existing tasks
  async bulkEnhanceTasks(userId: string, limit: number = 10): Promise<{
    processed: number;
    enhanced: number;
    errors: number;
  }> {
    if (!this.aiService) {
      throw new Error("AI service not available");
    }

    const taskRepo = this.repositoryFactory.getTaskRepository();
    const tasks = await taskRepo.findByUserId(userId, limit);

    let processed = 0;
    let enhanced = 0;
    let errors = 0;

    for (const task of tasks) {
      try {
        processed++;

        // Skip if already AI-enhanced
        if (task.metadata?.aiEnhanced) {
          continue;
        }

        const enhancement = await this.aiService.enhanceTask(task.title, task.description);

        await taskRepo.updateTask(task.id, {
          metadata: {
            source: task.metadata?.source || "direct",
            ...task.metadata,
            aiEnhanced: true,
            enhancementDate: new Date().toISOString(),
            suggestedSubtasks: enhancement.suggestedSubtasks,
            relatedTopics: enhancement.relatedTopics
          }
        });

        enhanced++;
      } catch (error) {
        console.error(`Failed to enhance task ${task.id}:`, error);
        errors++;
      }
    }

    return { processed, enhanced, errors };
  }

  // Get task by ID
  async getTaskById(taskId: string): Promise<Task | null> {
    const taskRepo = this.repositoryFactory.getTaskRepository();
    return taskRepo.findById(taskId);
  }

  // Get task by ID with user validation
  async getTask(taskId: string, userId: string): Promise<Task | null> {
    const taskRepo = this.repositoryFactory.getTaskRepository();
    const task = await taskRepo.findById(taskId);

    // Ensure the task belongs to the user
    if (task && task.userId === userId) {
      return task;
    }

    return null;
  }

  // Create a simple task
  async createTask(
    userId: string,
    title: string,
    description?: string,
    priority: TaskPriority = "medium",
    dueDate?: Date
  ): Promise<Task> {
    const taskRepo = this.repositoryFactory.getTaskRepository();

    const taskData = {
      userId,
      title: title.trim(),
      description: description?.trim(),
      status: "pending" as const,
      priority,
      dueDate: dueDate?.toISOString(),
      reminderDate: dueDate ? new Date(dueDate.getTime() - 24 * 60 * 60 * 1000).toISOString() : undefined, // 1 day before due date
      metadata: {
        source: "direct" as const,
        tags: [],
        createdVia: "api"
      }
    };

    return taskRepo.create(taskData);
  }

  // Update task
  async updateTask(
    taskId: string,
    userId: string,
    updates: Partial<Task>
  ): Promise<Task | null> {
    const taskRepo = this.repositoryFactory.getTaskRepository();

    // First check if the task belongs to the user
    const existingTask = await this.getTask(taskId, userId);
    if (!existingTask) {
      return null;
    }

    return taskRepo.updateTask(taskId, updates);
  }

  // Delete task
  async deleteTask(taskId: string, userId: string): Promise<boolean> {
    const taskRepo = this.repositoryFactory.getTaskRepository();

    // First verify the task belongs to the user
    const task = await taskRepo.findById(taskId);
    if (!task || task.userId !== userId) {
      return false;
    }

    // Use soft delete to preserve data integrity
    return await taskRepo.softDelete(taskId);
  }

  // Extract task information from message text
  private extractTaskInfo(messageText: string): {
    title: string;
    description?: string;
    priority: "low" | "medium" | "high";
    dueDate?: string;
    reminderDate?: string;
    extractedInfo: Record<string, any>;
  } {
    // Simple extraction logic - can be enhanced with AI later
    const lines = messageText.split("\n").filter(line => line?.trim());

    // Use first line as title, rest as description
    const title = lines[0]?.substring(0, 200) || "New Task";
    const description = lines.length > 1 ? lines.slice(1).join("\n").substring(0, 1000) : undefined;

    // Extract priority from keywords
    let priority: "low" | "medium" | "high" = "medium";
    const lowerText = messageText.toLowerCase();

    if (lowerText.includes("urgent") || lowerText.includes("asap") || lowerText.includes("important")) {
      priority = "high";
    } else if (lowerText.includes("low priority") || lowerText.includes("when possible")) {
      priority = "low";
    }

    // Extract dates (simple patterns)
    const dueDate = this.extractDueDate(messageText);
    const reminderDate = this.extractReminderDate(messageText, dueDate);

    return {
      title,
      description,
      priority,
      dueDate,
      reminderDate,
      extractedInfo: {
        originalLength: messageText.length,
        lineCount: lines.length,
        hasKeywords: {
          urgent: lowerText.includes("urgent"),
          important: lowerText.includes("important"),
          deadline: lowerText.includes("deadline"),
          reminder: lowerText.includes("remind")
        }
      }
    };
  }

  // Extract due date from text
  private extractDueDate(text: string): string | undefined {
    const lowerText = text.toLowerCase();
    const now = new Date();

    // Simple date extraction patterns
    if (lowerText.includes("today")) {
      return now.toISOString();
    }

    if (lowerText.includes("tomorrow")) {
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow.toISOString();
    }

    if (lowerText.includes("next week")) {
      const nextWeek = new Date(now);
      nextWeek.setDate(nextWeek.getDate() + 7);
      return nextWeek.toISOString();
    }

    // Look for date patterns like "by Friday", "due Monday", etc.
    const dayPattern = /(monday|tuesday|wednesday|thursday|friday|saturday|sunday)/i;
    const dayMatch = text.match(dayPattern);

    if (dayMatch) {
      const targetDay = this.getDayOfWeek(dayMatch[1].toLowerCase());
      const targetDate = this.getNextWeekday(now, targetDay);
      return targetDate.toISOString();
    }

    return undefined;
  }

  // Extract reminder date from text
  private extractReminderDate(text: string, dueDate?: string): string | undefined {
    if (!dueDate) {
      return undefined;
    }

    const lowerText = text.toLowerCase();
    const due = new Date(dueDate);

    // Default reminder: 1 day before due date
    if (lowerText.includes("remind")) {
      const reminder = new Date(due);
      reminder.setDate(reminder.getDate() - 1);
      return reminder.toISOString();
    }

    return undefined;
  }

  // Helper methods
  private getDayOfWeek(day: string): number {
    const days = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
    return days.indexOf(day);
  }

  private getNextWeekday(from: Date, targetDay: number): Date {
    const result = new Date(from);
    const currentDay = result.getDay();
    const daysUntilTarget = (targetDay - currentDay + 7) % 7;

    if (daysUntilTarget === 0) {
      // If it's the same day, assume next week
      result.setDate(result.getDate() + 7);
    } else {
      result.setDate(result.getDate() + daysUntilTarget);
    }

    return result;
  }

  private generateId(): string {
    return crypto.randomUUID();
  }
}
