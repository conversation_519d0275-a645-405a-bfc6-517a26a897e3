import * as z from "zod";

// Event type enum
export const EventTypeSchema = z.enum(["fixed", "calculated"]);

// Fixed date schema
export const FixedDateSchema = z.object({
  day: z.number().min(1).max(31),
  month: z.number().min(1).max(12)
});

// Calculation method enum
export const CalculationMethodSchema = z.enum(["easter", "orthodox_easter", "lunar", "custom"]);

// Christian event schema
export const ChristianEventSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  type: EventTypeSchema,
  fixedDate: FixedDateSchema.optional(),
  calculation: CalculationMethodSchema.optional(),
  reminderDays: z.array(z.number().min(0).max(365)).default([1, 7]),
  isActive: z.boolean().default(true),
  category: z.string().max(50).optional(),
  liturgicalColor: z.string().max(20).optional(),
  significance: z.enum(["major", "minor", "feast", "fast"]).optional(),
  createdAt: z.iso.datetime(),
  updatedAt: z.iso.datetime()
});

// Create event request schema
export const CreateEventSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  type: EventTypeSchema,
  fixedDate: FixedDateSchema.optional(),
  calculation: CalculationMethodSchema.optional(),
  reminderDays: z.array(z.number().min(0).max(365)).optional(),
  category: z.string().max(50).optional(),
  liturgicalColor: z.string().max(20).optional(),
  significance: z.enum(["major", "minor", "feast", "fast"]).optional()
}).refine(
  (data) => {
    if (data.type === "fixed") {
      return !!data.fixedDate;
    }
    if (data.type === "calculated") {
      return !!data.calculation;
    }
    return true;
  },
  {
    message: "Fixed events require fixedDate, calculated events require calculation method"
  }
);

// Update event request schema
export const UpdateEventSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  type: EventTypeSchema.optional(),
  fixedDate: FixedDateSchema.optional(),
  calculation: CalculationMethodSchema.optional(),
  reminderDays: z.array(z.number().min(0).max(365)).optional(),
  isActive: z.boolean().optional(),
  category: z.string().max(50).optional(),
  liturgicalColor: z.string().max(20).optional(),
  significance: z.enum(["major", "minor", "feast", "fast"]).optional()
});

// Query parameters schema
export const EventQuerySchema = z.object({
  type: EventTypeSchema.optional(),
  category: z.string().optional(),
  significance: z.enum(["major", "minor", "feast", "fast"]).optional(),
  upcoming: z.enum(["true", "false"]).optional(),
  days: z.coerce.number().min(1).max(365).optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
  offset: z.coerce.number().min(0).optional()
});

// Event ID parameter schema
export const EventParamsSchema = z.object({
  id: z.string().uuid()
});

// Event date calculation response
export const EventDateSchema = z.object({
  event: ChristianEventSchema,
  date: z.iso.datetime(),
  year: z.number(),
  daysUntil: z.number().optional()
});

// Response schemas
export const EventResponseSchema = z.object({
  success: z.boolean(),
  data: ChristianEventSchema,
  message: z.string().optional()
});

export const EventListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(ChristianEventSchema),
  pagination: z.object({
    total: z.number(),
    limit: z.number(),
    offset: z.number(),
    hasMore: z.boolean()
  }),
  message: z.string().optional()
});

export const UpcomingEventsResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(EventDateSchema),
  message: z.string().optional()
});


// Export types (now as single source of truth)
export type ChristianEvent = z.infer<typeof ChristianEventSchema>;
export type CreateEvent = z.infer<typeof CreateEventSchema>;
export type UpdateEvent = z.infer<typeof UpdateEventSchema>;
export type EventQuery = z.infer<typeof EventQuerySchema>;
export type EventParams = z.infer<typeof EventParamsSchema>;
export type EventDate = z.infer<typeof EventDateSchema>;
export type EventResponse = z.infer<typeof EventResponseSchema>;
export type EventListResponse = z.infer<typeof EventListResponseSchema>;
export type UpcomingEventsResponse = z.infer<typeof UpcomingEventsResponseSchema>;

// Type aliases for convenience
export type EventType = z.infer<typeof EventTypeSchema>;
export type CalculationMethod = z.infer<typeof CalculationMethodSchema>;
export type FixedDate = z.infer<typeof FixedDateSchema>;
