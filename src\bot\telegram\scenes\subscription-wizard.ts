// Subscription/Upgrade Wizard Scene

import { Scenes, Composer, Markup } from "telegraf";
import { TBotContext } from "../core/types";
import { BotServices } from "../core/core";
import { SCENES, SUBSCRIPTION_MESSAGES } from "../constants";

interface SubscriptionWizardSession extends Scenes.WizardSessionData {
  subscriptionData: {
    planId?: string;
    planName?: string;
    price?: number;
    interval?: string;
    trialDays?: number;
    paymentMethod?: string;
  };
}

export class SubscriptionWizard {
  private services: BotServices;

  constructor(services: BotServices) {
    this.services = services;
  }

  createScene(): Scenes.WizardScene<TBotContext> {
    const scene = new Scenes.WizardScene<TBotContext>(
      SCENES.SUBSCRIPTION,
      this.stepPlanSelection.bind(this),
      this.stepPaymentMethod.bind(this),
      this.stepConfirmation.bind(this)
    );

    scene.command("cancel", this.handleCancel.bind(this));
    scene.action("cancel", this.handleCancel.bind(this));
    scene.action("back", this.handleBack.bind(this));
    scene.action(/^plan_/, this.handlePlanSelection.bind(this));
    scene.action(/^payment_/, this.handlePaymentSelection.bind(this));

    return scene;
  }

  private async stepPlanSelection(ctx: TBotContext): Promise<void> {
    const currentTier = ctx.session?.user?.tier.type;

    if (currentTier === "paid") {
      await ctx.reply("💎 You already have Premium access! Use /subscription to manage your plan.");
      return ctx.scene.leave();
    }

    if (currentTier === "admin") {
      await ctx.reply("👑 You have Admin access with unlimited features.");
      return ctx.scene.leave();
    }

    ctx.wizard.state = { subscriptionData: {} } as SubscriptionWizardSession;

    await ctx.reply(SUBSCRIPTION_MESSAGES.PLAN_SELECTION, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("📅 Monthly ($9.99)", "plan_premium_monthly")],
        [Markup.button.callback("🔥 Yearly ($99.99)", "plan_premium_yearly")],
        [Markup.button.callback("📊 Compare Features", "compare_features")],
        [Markup.button.callback("❓ FAQ", "subscription_faq")],
        [Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepPaymentMethod(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as SubscriptionWizardSession;

    if (!wizardState.subscriptionData.planId) {
      await ctx.reply("⚠️ Please select a plan first.");
      return ctx.wizard.back();
    }

    const message = `💳 *Payment Method*

**Selected Plan:** ${wizardState.subscriptionData.planName}
**Price:** $${wizardState.subscriptionData.price}/${wizardState.subscriptionData.interval}
**Free Trial:** ${wizardState.subscriptionData.trialDays} days

Choose your payment method:`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("💳 Credit Card", "payment_card"),
          Markup.button.callback("🏦 PayPal", "payment_paypal")
        ],
        [
          Markup.button.callback("₿ Cryptocurrency", "payment_crypto"),
          Markup.button.callback("🍎 Apple Pay", "payment_apple")
        ],
        [
          Markup.button.callback("📱 Google Pay", "payment_google"),
          Markup.button.callback("🏪 Bank Transfer", "payment_bank")
        ],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepConfirmation(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as SubscriptionWizardSession;

    if (!wizardState.subscriptionData.paymentMethod) {
      await ctx.reply("⚠️ Please select a payment method first.");
      return ctx.wizard.back();
    }

    const paymentMethodNames = {
      card: "Credit Card",
      paypal: "PayPal",
      crypto: "Cryptocurrency",
      apple: "Apple Pay",
      google: "Google Pay",
      bank: "Bank Transfer"
    };

    const message = SUBSCRIPTION_MESSAGES.PAYMENT_PROCESSING
      .replace("{planName}", wizardState.subscriptionData.planName!)
      .replace("{planPrice}", wizardState.subscriptionData.price!.toString())
      .replace("{interval}", wizardState.subscriptionData.interval!)
      .replace("{trialDays}", wizardState.subscriptionData.trialDays!.toString());

    const additionalInfo = `
**Payment Method:** ${paymentMethodNames[wizardState.subscriptionData.paymentMethod! as keyof typeof paymentMethodNames]}

**Terms:**
• Free trial starts immediately
• No charge during trial period
• Cancel anytime before trial ends
• Auto-renewal after trial
• Secure payment processing`;

    await ctx.reply(message + additionalInfo, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("💳 Proceed to Payment", "confirm_payment")],
        [Markup.button.callback("📋 Terms & Conditions", "terms")],
        [Markup.button.callback("🔒 Privacy Policy", "privacy")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    if (ctx.callbackQuery && "data" in ctx.callbackQuery && ctx.callbackQuery.data === "confirm_payment") {
      await this.processPayment(ctx, wizardState);
    }
  }

  private async handlePlanSelection(ctx: TBotContext): Promise<void> {
    if (!ctx.callbackQuery || !("data" in ctx.callbackQuery)) {
      return;
    }

    const wizardState = ctx.wizard.state as SubscriptionWizardSession;
    const planId = ctx.callbackQuery.data.replace("plan_", "");

    // Set plan details based on selection
    if (planId === "premium_monthly") {
      wizardState.subscriptionData = {
        planId,
        planName: "Premium Monthly",
        price: 9.99,
        interval: "month",
        trialDays: 7
      };
    } else if (planId === "premium_yearly") {
      wizardState.subscriptionData = {
        planId,
        planName: "Premium Yearly",
        price: 99.99,
        interval: "year",
        trialDays: 14
      };
    }

    await ctx.answerCbQuery();
    return ctx.wizard.next();
  }

  private async handlePaymentSelection(ctx: TBotContext): Promise<void> {
    if (!ctx.callbackQuery || !("data" in ctx.callbackQuery)) {
      return;
    }

    const wizardState = ctx.wizard.state as SubscriptionWizardSession;
    const paymentMethod = ctx.callbackQuery.data.replace("payment_", "");

    wizardState.subscriptionData.paymentMethod = paymentMethod;

    await ctx.answerCbQuery();
    return ctx.wizard.next();
  }

  private async processPayment(ctx: TBotContext, wizardState: SubscriptionWizardSession): Promise<void> {
    try {
      await ctx.answerCbQuery();

      const userId = ctx.from?.id.toString();
      if (!userId) {
        return ctx.scene.leave();
      }

      // Create payment intent (this would integrate with actual payment processor)
      const paymentUrl = await this.createPaymentIntent(wizardState.subscriptionData);

      const message = `🚀 *Payment Processing*

**Plan:** ${wizardState.subscriptionData.planName}
**Amount:** $${wizardState.subscriptionData.price}
**Trial:** ${wizardState.subscriptionData.trialDays} days free

Click the button below to complete your payment securely:`;

      await ctx.reply(message, {
        parse_mode: "Markdown",
        ...Markup.inlineKeyboard([
          [Markup.button.url("💳 Complete Payment", paymentUrl)],
          [Markup.button.callback("❓ Payment Help", "payment_help")],
          [Markup.button.callback("❌ Cancel", "cancel")]
        ])
      });

      // Track subscription attempt
      await this.services.sessionManager.updateAnalytics(ctx, "subscription_attempt");

      // Set a timeout to check payment status
      setTimeout(async () => {
        await this.checkPaymentStatus(ctx, wizardState);
      }, 60000); // Check after 1 minute

      return ctx.scene.leave();
    } catch (error: any) {
      console.error("Error processing payment:", error);
      await ctx.reply("❌ Failed to process payment. Please try again or contact support.");
      return ctx.scene.leave();
    }
  }

  private async createPaymentIntent(subscriptionData: any): Promise<string> {
    // This would integrate with actual payment processors like Stripe, PayPal, etc.
    // For now, return a mock payment URL
    const paymentId = `payment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return `https://payment.example.com/pay/${paymentId}?plan=${subscriptionData.planId}&amount=${subscriptionData.price}`;
  }

  private async checkPaymentStatus(ctx: TBotContext, wizardState: SubscriptionWizardSession): Promise<void> {
    // This would check with the payment processor for payment status
    // For now, we'll simulate a successful payment check

    const paymentSuccessful = Math.random() > 0.3; // 70% success rate for demo

    if (paymentSuccessful) {
      await ctx.reply(`🎉 *Payment Successful!*

Welcome to Premium! Your ${wizardState.subscriptionData.trialDays}-day free trial has started.

**Premium Features Unlocked:**
✅ Unlimited tasks & birthdays
✅ ${wizardState.subscriptionData.planId === "premium_yearly" ? "200" : "100"} AI requests/day
✅ Advanced notifications
✅ Data export
✅ Task templates
✅ Priority support

Use /subscription to manage your plan.`, {
        parse_mode: "Markdown"
      });

      // Update user tier (this would be done by the subscription manager)
      await this.services.sessionManager.updateAnalytics(ctx, "subscription_successful");
    } else {
      await ctx.reply(`⏳ *Payment Pending*

We're still processing your payment. You'll receive a confirmation once it's complete.

If you have any issues, please contact support with your payment reference.`, {
        parse_mode: "Markdown",
        reply_markup: {
          inline_keyboard: [
            [Markup.button.callback("🔄 Check Status", "check_payment")],
            [Markup.button.callback("💬 Contact Support", "contact_support")]
          ]
        }
      });
    }
  }

  private async handleCancel(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    await ctx.reply("❌ Subscription process cancelled. You can upgrade anytime using /upgrade.");
    return ctx.scene.leave();
  }

  private async handleBack(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    return ctx.wizard.back();
  }
}
