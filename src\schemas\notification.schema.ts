import * as z from "zod";

// Notification type enum (unified naming - resolves inconsistency)
export const NotificationTypeSchema = z.enum([
  "task-deadline",
  "birthday",
  "christian-event",
  "task-reminder",
  "event-reminder",
  "custom"
]);

// Notification status enum (enhanced with 'snoozed' status)
export const NotificationStatusSchema = z.enum(["pending", "sent", "failed", "cancelled", "snoozed"]);

// Notification metadata schema
export const NotificationMetadataSchema = z.object({
  originalSchedule: z.iso.datetime().optional(),
  snoozeCount: z.number().min(0).default(0),
  deliveryAttempts: z.number().min(0).default(0),
  userTimezone: z.string().optional(),
  lastFailureTime: z.iso.datetime().optional(),
  retryCount: z.number().min(0).default(0)
}).optional();

// Notification schema (enhanced to match core types structure)
export const NotificationSchema = z.object({
  id: z.string().uuid(),
  userId: z.string(),
  type: NotificationTypeSchema,
  entityId: z.string(), // Reference to Task.id, Birthday.id, or ChristianEvent.id (matches core types)
  scheduledFor: z.iso.datetime(),
  sentAt: z.iso.datetime().optional(),
  status: NotificationStatusSchema.default("pending"),
  message: z.string().min(1).max(1000),
  createdAt: z.iso.datetime(),
  priority: z.enum(["low", "medium", "high"]).optional(),
  metadata: NotificationMetadataSchema
});

// Create notification request schema
export const CreateNotificationSchema = z.object({
  type: NotificationTypeSchema,
  title: z.string().min(1).max(200),
  message: z.string().min(1).max(1000),
  scheduledFor: z.iso.datetime(),
  metadata: NotificationMetadataSchema,
  relatedEntityId: z.string().optional(),
  relatedEntityType: z.enum(["task", "birthday", "event"]).optional()
});

// Update notification request schema
export const UpdateNotificationSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  message: z.string().min(1).max(1000).optional(),
  scheduledFor: z.iso.datetime().optional(),
  status: NotificationStatusSchema.optional(),
  metadata: NotificationMetadataSchema
});

// Query parameters schema
export const NotificationQuerySchema = z.object({
  type: NotificationTypeSchema.optional(),
  status: NotificationStatusSchema.optional(),
  from: z.iso.datetime().optional(),
  to: z.iso.datetime().optional(),
  relatedEntityType: z.enum(["task", "birthday", "event"]).optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
  offset: z.coerce.number().min(0).optional()
});

// Notification ID parameter schema
export const NotificationParamsSchema = z.object({
  id: z.string().uuid()
});

// Snooze notification request schema
export const SnoozeNotificationSchema = z.object({
  minutes: z.number().min(1).max(10080) // Max 1 week
});

// Bulk action schema
export const BulkNotificationActionSchema = z.object({
  action: z.enum(["cancel", "reschedule"]),
  notificationIds: z.array(z.string().uuid()).min(1),
  newScheduledFor: z.iso.datetime().optional() // Required for reschedule action
}).refine(
  (data) => {
    if (data.action === "reschedule") {
      return !!data.newScheduledFor;
    }
    return true;
  },
  {
    message: "Reschedule action requires newScheduledFor date"
  }
);

// Response schemas
export const NotificationResponseSchema = z.object({
  success: z.boolean(),
  data: NotificationSchema,
  message: z.string().optional()
});

export const NotificationListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(NotificationSchema),
  pagination: z.object({
    total: z.number(),
    limit: z.number(),
    offset: z.number(),
    hasMore: z.boolean()
  }),
  message: z.string().optional()
});

export const BulkActionResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    processed: z.number(),
    successful: z.number(),
    failed: z.number(),
    errors: z.array(z.string()).optional()
  }),
  message: z.string().optional()
});


// Additional schemas to replace all notification-related types

// Notification preferences schema
export const NotificationPreferencesSchema = z.object({
  enabled: z.boolean(),
  timezone: z.string(),
  quietHours: z.object({
    start: z.string(), // HH:MM format
    end: z.string()   // HH:MM format
  }),
  intervals: z.object({
    taskDeadline: z.array(z.number()), // Hours before deadline [24, 1]
    birthday: z.array(z.number()),     // Days before birthday [7, 1]
    christianEvent: z.array(z.number()) // Days before event [3, 1]
  }),
  maxDailyNotifications: z.number(),
  snoozeMinutes: z.number()
});

// Export types (now as single source of truth)
export type Notification = z.infer<typeof NotificationSchema>;
export type CreateNotification = z.infer<typeof CreateNotificationSchema>;
export type UpdateNotification = z.infer<typeof UpdateNotificationSchema>;
export type NotificationQuery = z.infer<typeof NotificationQuerySchema>;
export type NotificationParams = z.infer<typeof NotificationParamsSchema>;
export type SnoozeNotification = z.infer<typeof SnoozeNotificationSchema>;
export type BulkNotificationAction = z.infer<typeof BulkNotificationActionSchema>;
export type NotificationResponse = z.infer<typeof NotificationResponseSchema>;
export type NotificationListResponse = z.infer<typeof NotificationListResponseSchema>;
export type BulkActionResponse = z.infer<typeof BulkActionResponseSchema>;

// Additional type exports
export type NotificationPreferences = z.infer<typeof NotificationPreferencesSchema>;

// Type aliases for convenience (unified naming)
export type NotificationStatus = z.infer<typeof NotificationStatusSchema>;
export type NotificationType = z.infer<typeof NotificationTypeSchema>;
