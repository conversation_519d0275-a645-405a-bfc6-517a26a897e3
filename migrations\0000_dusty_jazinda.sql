CREATE TABLE `attachments` (
	`id` text PRIMARY KEY NOT NULL,
	`taskId` text NOT NULL,
	`type` text NOT NULL,
	`fileId` text NOT NULL,
	`fileName` text,
	`mimeType` text,
	`fileSize` integer,
	`uploadedAt` text NOT NULL,
	FOREIGN KEY (`taskId`) REFERENCES `tasks`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `idx_attachments_taskId` ON `attachments` (`taskId`);--> statement-breakpoint
CREATE INDEX `idx_attachments_type` ON `attachments` (`type`);--> statement-breakpoint
CREATE TABLE `birthdays` (
	`id` text PRIMARY KEY NOT NULL,
	`userId` text NOT NULL,
	`name` text NOT NULL,
	`day` integer NOT NULL,
	`month` integer NOT NULL,
	`year` integer,
	`reminderDays` text DEFAULT '[1,7]' NOT NULL,
	`suggestions` text DEFAULT '[]' NOT NULL,
	`createdAt` text NOT NULL,
	`updatedAt` text NOT NULL,
	`active` integer DEFAULT true NOT NULL,
	FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `idx_birthdays_userId` ON `birthdays` (`userId`);--> statement-breakpoint
CREATE INDEX `idx_birthdays_date` ON `birthdays` (`month`,`day`);--> statement-breakpoint
CREATE INDEX `idx_birthdays_active` ON `birthdays` (`active`);--> statement-breakpoint
CREATE INDEX `idx_birthdays_user_active` ON `birthdays` (`userId`,`active`);--> statement-breakpoint
CREATE TABLE `christian_events` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`type` text NOT NULL,
	`fixedDate` text,
	`calculation` text,
	`reminderDays` text DEFAULT '[1,7]' NOT NULL,
	`recommendations` text DEFAULT '[]' NOT NULL,
	`description` text,
	`createdAt` text NOT NULL,
	`updatedAt` text NOT NULL,
	`active` integer DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `christian_events_name_unique` ON `christian_events` (`name`);--> statement-breakpoint
CREATE INDEX `idx_events_type` ON `christian_events` (`type`);--> statement-breakpoint
CREATE INDEX `idx_events_active` ON `christian_events` (`active`);--> statement-breakpoint
CREATE INDEX `idx_events_name` ON `christian_events` (`name`);--> statement-breakpoint
CREATE TABLE `notifications` (
	`id` text PRIMARY KEY NOT NULL,
	`userId` text NOT NULL,
	`type` text NOT NULL,
	`entityId` text NOT NULL,
	`scheduledFor` text NOT NULL,
	`sentAt` text,
	`status` text DEFAULT 'pending' NOT NULL,
	`message` text NOT NULL,
	`createdAt` text NOT NULL,
	FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `idx_notifications_userId` ON `notifications` (`userId`);--> statement-breakpoint
CREATE INDEX `idx_notifications_status` ON `notifications` (`status`);--> statement-breakpoint
CREATE INDEX `idx_notifications_scheduledFor` ON `notifications` (`scheduledFor`);--> statement-breakpoint
CREATE INDEX `idx_notifications_type` ON `notifications` (`type`);--> statement-breakpoint
CREATE INDEX `idx_notifications_entityId` ON `notifications` (`entityId`);--> statement-breakpoint
CREATE INDEX `idx_notifications_pending` ON `notifications` (`status`,`scheduledFor`);--> statement-breakpoint
CREATE TABLE `tasks` (
	`id` text PRIMARY KEY NOT NULL,
	`userId` text NOT NULL,
	`title` text NOT NULL,
	`description` text,
	`status` text DEFAULT 'pending' NOT NULL,
	`priority` text DEFAULT 'medium' NOT NULL,
	`dueDate` text,
	`reminderDate` text,
	`createdAt` text NOT NULL,
	`updatedAt` text NOT NULL,
	`completedAt` text,
	`metadata` text DEFAULT '{"source":"direct"}' NOT NULL,
	`attachments` text,
	FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `idx_tasks_userId` ON `tasks` (`userId`);--> statement-breakpoint
CREATE INDEX `idx_tasks_status` ON `tasks` (`status`);--> statement-breakpoint
CREATE INDEX `idx_tasks_dueDate` ON `tasks` (`dueDate`);--> statement-breakpoint
CREATE INDEX `idx_tasks_priority` ON `tasks` (`priority`);--> statement-breakpoint
CREATE INDEX `idx_tasks_createdAt` ON `tasks` (`createdAt`);--> statement-breakpoint
CREATE INDEX `idx_tasks_user_status` ON `tasks` (`userId`,`status`);--> statement-breakpoint
CREATE TABLE `users` (
	`id` text PRIMARY KEY NOT NULL,
	`username` text,
	`firstName` text NOT NULL,
	`lastName` text,
	`createdAt` text NOT NULL,
	`lastActive` text NOT NULL,
	`preferences` text DEFAULT '{"timezone":"UTC","notificationTime":"09:00","mode":"chat"}' NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_users_username` ON `users` (`username`);--> statement-breakpoint
CREATE INDEX `idx_users_lastActive` ON `users` (`lastActive`);