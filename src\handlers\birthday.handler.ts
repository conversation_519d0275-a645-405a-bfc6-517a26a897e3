import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator as z<PERSON><PERSON><PERSON>tor } from "hono-openapi/zod";
import { RepositoryFactory } from "../repositories";
import {
  BirthdayListResponseSchema,
  BirthdayQuerySchema,
  BirthdayResponseSchema,
  CreateBirthdaySchema
} from "../schemas/birthday.schema";
import { ErrorResponseSchema } from "../schemas/common.schema";
import { BirthdayService } from "../services/birthday.service";

const router = new Hono<{ Bindings: CloudflareBindings }>();

// GET /api/birthdays - Get user birthdays
router.get(
  "/",
  describeRoute({
    description: "Get user birthdays with optional filtering",
    responses: {
      200: {
        description: "Successfully retrieved birthdays",
        content: {
          "application/json": {
            schema: resolver(BirthdayListResponseSchema.meta({
              example: {
                success: true,
                data: [{
                  id: "123e4567-e89b-12d3-a456-426614174000",
                  userId: "user123",
                  name: "<PERSON>",
                  date: { day: 15, month: 6 },
                  year: 1990,
                  reminderDays: [1, 7],
                  isActive: true,
                  createdAt: "2024-01-15T10:00:00Z",
                  updatedAt: "2024-01-15T10:00:00Z"
                }],
                pagination: {
                  total: 1,
                  limit: 10,
                  offset: 0,
                  hasMore: false
                }
              }
            }))
          }
        }
      },
      400: {
        description: "Bad request - User ID required",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("query", BirthdayQuerySchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const query = c.req.valid("query");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const birthdayService = new BirthdayService(repositoryFactory);

      const birthdays = await birthdayService.getUserBirthdays(userId);

      return c.json({
        success: true,
        data: birthdays,
        pagination: {
          total: birthdays.length,
          limit: query.limit || 10,
          offset: query.offset || 0,
          hasMore: birthdays.length === (query.limit || 10)
        }
      });
    } catch (error: any) {
      console.error("Get birthdays error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

// POST /api/birthdays - Create new birthday
router.post(
  "/",
  describeRoute({
    description: "Create a new birthday",
    responses: {
      201: {
        description: "Birthday created successfully",
        content: {
          "application/json": {
            schema: resolver(BirthdayResponseSchema)
          }
        }
      },
      400: {
        description: "Bad request - validation error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      },
      500: {
        description: "Internal server error",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  zValidator("json", CreateBirthdaySchema),
  async (c) => {
    try {
      const userId = c.req.header("X-User-ID");
      if (!userId) {
        return c.json({ success: false, error: "User ID required" }, 400);
      }

      const body = c.req.valid("json");
      const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
      const birthdayService = new BirthdayService(repositoryFactory);

      const birthday = await birthdayService.addBirthday(
        userId,
        body.name,
        body.date.day,
        body.date.month,
        body.year,
        body.reminderDays
      );

      return c.json({
        success: true,
        data: birthday
      }, 201);
    } catch (error: any) {
      console.error("Create birthday error:", error);
      return c.json({ success: false, error: error.message }, 500);
    }
  }
);

// GET /api/birthdays/upcoming - Get upcoming birthdays
router.get("/upcoming", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const days = parseInt(c.req.query("days") || "30");

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const birthdayService = new BirthdayService(repositoryFactory);

    const upcomingBirthdays = await birthdayService.getUpcomingBirthdays(userId, days);

    return c.json({
      success: true,
      data: upcomingBirthdays,
      count: upcomingBirthdays.length
    });
  } catch (error: any) {
    console.error("Get upcoming birthdays error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// GET /api/birthdays/:id - Get specific birthday
router.get("/:id", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    const birthdayId = c.req.param("id");

    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const birthdayService = new BirthdayService(repositoryFactory);

    const birthday = await birthdayService.getBirthday(birthdayId, userId);

    if (!birthday) {
      return c.json({ error: "Birthday not found" }, 404);
    }

    return c.json({
      success: true,
      data: birthday
    });
  } catch (error: any) {
    console.error("Get birthday error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// PUT /api/birthdays/:id - Update birthday
router.put("/:id", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    const birthdayId = c.req.param("id");

    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const body = await c.req.json();
    const updates = body;

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const birthdayService = new BirthdayService(repositoryFactory);

    const birthday = await birthdayService.updateBirthday(birthdayId, userId, updates);

    return c.json({
      success: true,
      data: birthday
    });
  } catch (error: any) {
    console.error("Update birthday error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// DELETE /api/birthdays/:id - Delete birthday
router.delete("/:id", async (c) => {
  try {
    const userId = c.req.header("X-User-ID");
    const birthdayId = c.req.param("id");

    if (!userId) {
      return c.json({ error: "User ID required" }, 400);
    }

    const repositoryFactory = new RepositoryFactory(c.env.BOT_DB);
    const birthdayService = new BirthdayService(repositoryFactory);

    await birthdayService.deleteBirthday(birthdayId, userId);

    return c.json({
      success: true,
      message: "Birthday deleted successfully"
    });
  } catch (error: any) {
    console.error("Delete birthday error:", error);
    return c.json({ error: error.message }, 500);
  }
});

export default router;
