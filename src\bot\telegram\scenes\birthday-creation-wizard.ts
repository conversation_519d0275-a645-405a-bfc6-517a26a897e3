// Birthday Creation Wizard Scene

import { Scene<PERSON>, Composer, Markup } from "telegraf";
import { TBotContext } from "../core/types";
import { BotServices } from "../core/core";
import { WIZARD_MESSAGES, VALIDATION_MESSAGES, SUCCESS_MESSAGES, SCENES } from "../constants";

interface BirthdayData {
  name?: string;
  day?: number;
  month?: number;
  year?: number;
  reminderDays?: number[];

}

interface BirthdayWizardSession extends Scenes.WizardSessionData {
  birthdayData: BirthdayData
}


export class BirthdayCreationWizard {
  private services: BotServices;

  constructor(services: BotServices) {
    this.services = services;
  }

  createScene(): Scenes.WizardScene<TBotContext> {
    const scene = new Scenes.WizardScene<TBotContext>(
      SCENES.BIRTHDAY_CREATION,
      this.stepName.bind(this),
      this.stepDate.bind(this),
      this.stepReminders.bind(this),
      this.stepConfirmation.bind(this)
    );

    scene.command("cancel", this.handleCancel.bind(this));
    scene.action("cancel", this.handleCancel.bind(this));
    scene.action("back", this.handleBack.bind(this));
    scene.action("skip_reminders", this.skipReminders.bind(this));

    return scene;
  }

  private async stepName(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    const canAddBirthday = await this.services.sessionManager.checkQuota(ctx, "birthdays");
    if (!canAddBirthday) {
      await ctx.reply("🎂 You've reached your birthday limit. Upgrade to add unlimited birthdays!");
      return ctx.scene.leave();
    }

    ctx.wizard.state.birthdayData = {};

    await ctx.reply(WIZARD_MESSAGES.BIRTHDAY_CREATION.NAME_PROMPT, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepDate(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state;

    if (ctx.message && "text" in ctx.message) {
      const name = ctx.message.text.trim();

      if (name.length < 2) {
        await ctx.reply("❌ Name is too short. Please enter a valid name:");
        return;
      }

      if (name.length > 50) {
        await ctx.reply(VALIDATION_MESSAGES.NAME_TOO_LONG);
        return;
      }

      wizardState.birthdayData.name = name;
    } else {
      await ctx.reply(VALIDATION_MESSAGES.TEXT_REQUIRED);
      return;
    }

    const message = WIZARD_MESSAGES.BIRTHDAY_CREATION.DATE_STEP.replace("{name}", wizardState.birthdayData.name!);

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepReminders(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as BirthdayWizardSession;

    if (ctx.message && "text" in ctx.message) {
      const dateText = ctx.message.text.trim();

      try {
        const parsedDate = this.parseDate(dateText);
        wizardState.birthdayData.day = parsedDate.day;
        wizardState.birthdayData.month = parsedDate.month;
        wizardState.birthdayData.year = parsedDate.year;
      } catch (error) {
        await ctx.reply(VALIDATION_MESSAGES.INVALID_DATE);
        return;
      }
    } else {
      await ctx.reply(VALIDATION_MESSAGES.TEXT_REQUIRED);
      return;
    }

    const dateStr = wizardState.birthdayData.year ?
      `${wizardState.birthdayData.day}/${wizardState.birthdayData.month}/${wizardState.birthdayData.year}` :
      `${wizardState.birthdayData.day}/${wizardState.birthdayData.month}`;

    const message = WIZARD_MESSAGES.BIRTHDAY_CREATION.REMINDERS_STEP
      .replace("{name}", wizardState.birthdayData.name!)
      .replace("{date}", dateStr);

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("1 day before", "reminder_1"),
          Markup.button.callback("1 week before", "reminder_7")
        ],
        [
          Markup.button.callback("Both (1 day & 1 week)", "reminder_both"),
          Markup.button.callback("Custom", "reminder_custom")
        ],
        [Markup.button.callback("⏭️ Skip Reminders", "skip_reminders")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepConfirmation(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as BirthdayWizardSession;

    if (ctx.callbackQuery && "data" in ctx.callbackQuery) {
      const data = ctx.callbackQuery.data;

      if (data === "reminder_1") {
        wizardState.birthdayData.reminderDays = [1];
      } else if (data === "reminder_7") {
        wizardState.birthdayData.reminderDays = [7];
      } else if (data === "reminder_both") {
        wizardState.birthdayData.reminderDays = [1, 7];
      } else if (data === "reminder_custom") {
        await ctx.answerCbQuery();
        await ctx.reply("Please enter reminder days separated by commas (e.g., \"1,3,7\"):");
        return;
      }

      await ctx.answerCbQuery();
    } else if (ctx.message && "text" in ctx.message) {
      // Handle custom reminder input
      try {
        const reminderDays = ctx.message.text.split(",").map(d => parseInt(d.trim())).filter(d => d > 0 && d <= 365);
        if (reminderDays.length === 0) {
          throw new Error("Invalid reminder days");
        }
        wizardState.birthdayData.reminderDays = reminderDays;
      } catch (error) {
        await ctx.reply("⚠️ Invalid reminder format. Please enter numbers separated by commas (e.g., \"1,3,7\"):");
        return;
      }
    }

    // Set default reminders if none selected
    if (!wizardState.birthdayData.reminderDays) {
      wizardState.birthdayData.reminderDays = [1, 7];
    }

    const dateStr = wizardState.birthdayData.year ?
      `${wizardState.birthdayData.day}/${wizardState.birthdayData.month}/${wizardState.birthdayData.year}` :
      `${wizardState.birthdayData.day}/${wizardState.birthdayData.month}`;

    const remindersStr = wizardState.birthdayData.reminderDays.map(d => `${d} day${d > 1 ? "s" : ""} before`).join(", ");

    const message = WIZARD_MESSAGES.BIRTHDAY_CREATION.CONFIRMATION_STEP
      .replace("{name}", wizardState.birthdayData.name!)
      .replace("{date}", dateStr)
      .replace("{reminders}", remindersStr);

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("✅ Add Birthday", "confirm_create")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    if (ctx.callbackQuery && "data" in ctx.callbackQuery && ctx.callbackQuery.data === "confirm_create") {
      await this.createBirthday(ctx, wizardState);
    }
  }

  private async createBirthday(ctx: TBotContext, wizardState: BirthdayWizardSession): Promise<void> {
    try {
      await ctx.answerCbQuery();

      await this.services.sessionManager.consumeQuota(ctx, "birthdays");

      const birthday = await this.services.birthdayService.addBirthday(
        ctx.from!.id.toString(),
        wizardState.birthdayData.name!,
        wizardState.birthdayData.day!,
        wizardState.birthdayData.month!,
        wizardState.birthdayData.year,
        wizardState.birthdayData.reminderDays!
      );

      const successMessage = `${SUCCESS_MESSAGES.BIRTHDAY_ADDED}

**${birthday.name}**
Date: ${birthday.day}/${birthday.month}${birthday.year ? `/${birthday.year}` : ""}
Reminders: ${birthday.reminderDays.map(d => `${d} day${d > 1 ? "s" : ""} before`).join(", ")}

Use /birthdays to view all your birthdays.`;

      await ctx.reply(successMessage, { parse_mode: "Markdown" });

      await this.services.sessionManager.updateAnalytics(ctx, "birthday_created");

      return ctx.scene.leave();
    } catch (error: any) {
      console.error("Error creating birthday:", error);
      await ctx.reply("❌ Failed to create birthday. Please try again.");
      return ctx.scene.leave();
    }
  }

  private async handleCancel(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    await ctx.reply("❌ Birthday creation cancelled.");
    return ctx.scene.leave();
  }

  private async handleBack(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    return ctx.wizard.back();
  }

  private async skipReminders(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery();
    const wizardState = ctx.wizard.state as BirthdayWizardSession;
    wizardState.birthdayData.reminderDays = [1, 7]; // Default reminders
    return ctx.wizard.next();
  }

  private parseDate(dateText: string): { day: number; month: number; year?: number } {
    // Simple date parsing - in production you'd use a proper date parsing library
    const patterns = [
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // DD/MM/YYYY
      /^(\d{1,2})\/(\d{1,2})$/, // DD/MM
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD
      /^(\d{1,2})-(\d{1,2})-(\d{4})$/ // DD-MM-YYYY
    ];

    for (const pattern of patterns) {
      const match = dateText.match(pattern);
      if (match) {
        if (pattern.source.includes("YYYY")) {
          if (pattern.source.startsWith("^\\(\\d\\{4\\}")) {
            // YYYY-MM-DD format
            const year = parseInt(match[1]);
            const month = parseInt(match[2]);
            const day = parseInt(match[3]);
            return { day, month, year };
          } else {
            // DD/MM/YYYY or DD-MM-YYYY format
            const day = parseInt(match[1]);
            const month = parseInt(match[2]);
            const year = parseInt(match[3]);
            return { day, month, year };
          }
        } else {
          // DD/MM format
          const day = parseInt(match[1]);
          const month = parseInt(match[2]);
          return { day, month };
        }
      }
    }

    // Try natural language parsing
    const lowerText = dateText.toLowerCase();
    const monthNames = ["january", "february", "march", "april", "may", "june",
      "july", "august", "september", "october", "november", "december"];

    for (let i = 0; i < monthNames.length; i++) {
      if (lowerText.includes(monthNames[i])) {
        const dayMatch = dateText.match(/\b(\d{1,2})\b/);
        const yearMatch = dateText.match(/\b(\d{4})\b/);

        if (dayMatch) {
          const day = parseInt(dayMatch[1]);
          const month = i + 1;
          const year = yearMatch ? parseInt(yearMatch[1]) : undefined;
          return { day, month, year };
        }
      }
    }

    throw new Error("Invalid date format");
  }
}
