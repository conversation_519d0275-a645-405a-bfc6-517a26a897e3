// Task Creation Wizard Scene using Telegraf's advanced patterns

import { Scenes, Composer, Markup } from "telegraf";
import { TBotContext } from "../core/types";
import { BotServices } from "../core/core";
import { SCENES } from "../constants";

interface TaskWizardSession extends Scenes.WizardSession {
  taskData: {
    title?: string;
    description?: string;
    priority?: "low" | "medium" | "high";
    dueDate?: Date;
    useAI?: boolean;
    enhancementLevel?: "basic" | "full";
  };
}

export class TaskCreationWizard {
  private services: BotServices;

  constructor(services: BotServices) {
    this.services = services;
  }

  createScene(): Scenes.WizardScene<TBotContext> {
    const scene = new Scenes.WizardScene<TBotContext>(
      SCENES.TASK_CREATION,
      this.stepTitle.bind(this),
      this.stepDescription.bind(this),
      this.stepPriority.bind(this),
      this.stepDueDate.bind(this),
      this.stepAIEnhancement.bind(this),
      this.stepConfirmation.bind(this)
    );

    // Handle cancellation at any step
    scene.command("cancel", this.handleCancel.bind(this));
    scene.action("cancel", this.handleCancel.bind(this));

    // Handle back navigation
    scene.action("back", this.handleBack.bind(this));

    // Handle skip actions
    scene.action("skip_description", this.skipDescription.bind(this));
    scene.action("skip_due_date", this.skipDueDate.bind(this));

    return scene;
  }

  /**
   * Step 1: Get task title
   */
  private async stepTitle(ctx: TBotContext): Promise<void> {
    // Check quota before starting
    const canCreateTask = await this.services.sessionManager.checkQuota(ctx, "tasks");
    if (!canCreateTask) {
      await ctx.reply("📊 You've reached your task limit. Upgrade to create more tasks!");
      return ctx.scene.leave();
    }

    // Initialize wizard session
    ctx.wizard.state = { taskData: {} } as TaskWizardSession;

    const message = `
📝 *Create New Task* (Step 1/6)

Please enter the task title:

*Examples:*
• "Buy groceries for dinner"
• "Finish project presentation"
• "Call dentist for appointment"
    `;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  /**
   * Step 2: Get task description (optional)
   */
  private async stepDescription(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as TaskWizardSession;

    if (ctx.message && "text" in ctx.message) {
      const title = ctx.message.text.trim();

      if (title.length < 3) {
        await ctx.reply("⚠️ Task title must be at least 3 characters long. Please try again:");
        return;
      }

      if (title.length > 100) {
        await ctx.reply("⚠️ Task title is too long (max 100 characters). Please shorten it:");
        return;
      }

      wizardState.taskData.title = title;
    } else {
      await ctx.reply("⚠️ Please enter a text message for the task title:");
      return;
    }

    const message = `
📝 *Create New Task* (Step 2/6)

**Title:** ${wizardState.taskData.title}

Now, please enter a description for your task (optional):

*This helps you remember details and allows AI to provide better suggestions.*
    `;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("⏭️ Skip Description", "skip_description")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  /**
   * Step 3: Set task priority
   */
  private async stepPriority(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as TaskWizardSession;

    if (ctx.message && "text" in ctx.message) {
      const description = ctx.message.text.trim();

      if (description.length > 500) {
        await ctx.reply("⚠️ Description is too long (max 500 characters). Please shorten it:");
        return;
      }

      wizardState.taskData.description = description;
    }

    const message = `
📝 *Create New Task* (Step 3/6)

**Title:** ${wizardState.taskData.title}
**Description:** ${wizardState.taskData.description || "None"}

Please select the task priority:
    `;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("🔴 High", "priority_high"),
          Markup.button.callback("🟡 Medium", "priority_medium"),
          Markup.button.callback("🟢 Low", "priority_low")
        ],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    // Handle priority selection
    ctx.wizard.state.priorityHandler = async (ctx: TBotContext) => {
      if (ctx.callbackQuery && "data" in ctx.callbackQuery) {
        const priority = ctx.callbackQuery.data.replace("priority_", "") as "high" | "medium" | "low";
        wizardState.taskData.priority = priority;
        await ctx.answerCbQuery();
        return ctx.wizard.next();
      }
    };

    return ctx.wizard.next();
  }

  /**
   * Step 4: Set due date (optional)
   */
  private async stepDueDate(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as TaskWizardSession;

    // Handle priority selection from previous step
    if (ctx.callbackQuery && "data" in ctx.callbackQuery && ctx.callbackQuery.data.startsWith("priority_")) {
      const priority = ctx.callbackQuery.data.replace("priority_", "") as "high" | "medium" | "low";
      wizardState.taskData.priority = priority;
      await ctx.answerCbQuery();
    }

    const priorityEmoji = {
      high: "🔴",
      medium: "🟡",
      low: "🟢"
    };

    const message = `
📝 *Create New Task* (Step 4/6)

**Title:** ${wizardState.taskData.title}
**Description:** ${wizardState.taskData.description || "None"}
**Priority:** ${priorityEmoji[wizardState.taskData.priority || "medium"]} ${wizardState.taskData.priority || "medium"}

Please enter a due date (optional):

*Format examples:*
• "tomorrow"
• "next Friday"
• "2024-12-25"
• "in 3 days"
    `;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("⏭️ Skip Due Date", "skip_due_date")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  /**
   * Step 5: AI Enhancement options
   */
  private async stepAIEnhancement(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as TaskWizardSession;

    if (ctx.message && "text" in ctx.message) {
      const dueDateText = ctx.message.text.trim();

      // Parse due date (simplified - you might want to use a proper date parsing library)
      try {
        const dueDate = this.parseDueDate(dueDateText);
        wizardState.taskData.dueDate = dueDate;
      } catch (error) {
        await ctx.reply("⚠️ Invalid date format. Please try again or skip this step:");
        return;
      }
    }

    // Check if user has AI access
    const hasAIAccess = this.services.sessionManager.hasFeatureAccess(ctx, "enhanced_ai") ||
      this.services.sessionManager.hasFeatureAccess(ctx, "basic_ai");

    if (!hasAIAccess) {
      // Skip AI step for users without access
      wizardState.taskData.useAI = false;
      return ctx.wizard.next();
    }

    const canUseAI = await this.services.sessionManager.checkQuota(ctx, "aiRequests");

    const message = `
📝 *Create New Task* (Step 5/6)

**Title:** ${wizardState.taskData.title}
**Priority:** ${wizardState.taskData.priority || "medium"}
**Due Date:** ${wizardState.taskData.dueDate ? wizardState.taskData.dueDate.toLocaleDateString() : "None"}

🤖 **AI Enhancement Options:**

${canUseAI ?
        "Would you like AI to enhance your task with suggestions and improvements?" :
        "⚠️ You've reached your AI quota limit. Upgrade for more AI features!"
      }
    `;

    const keyboard = canUseAI ? [
      [
        Markup.button.callback("🚀 Full Enhancement", "ai_full"),
        Markup.button.callback("⚡ Basic Enhancement", "ai_basic")
      ],
      [Markup.button.callback("⏭️ Skip AI", "ai_skip")],
      [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
    ] : [
      [Markup.button.callback("⏭️ Continue without AI", "ai_skip")],
      [Markup.button.callback("💎 Upgrade for AI", "upgrade")],
      [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
    ];

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard(keyboard)
    });

    return ctx.wizard.next();
  }

  /**
   * Step 6: Final confirmation and task creation
   */
  private async stepConfirmation(ctx: TBotContext): Promise<void> {
    const wizardState = ctx.wizard.state as TaskWizardSession;

    // Handle AI enhancement selection
    if (ctx.callbackQuery && "data" in ctx.callbackQuery) {
      const data = ctx.callbackQuery.data;

      if (data === "ai_full") {
        wizardState.taskData.useAI = true;
        wizardState.taskData.enhancementLevel = "full";
      } else if (data === "ai_basic") {
        wizardState.taskData.useAI = true;
        wizardState.taskData.enhancementLevel = "basic";
      } else if (data === "ai_skip") {
        wizardState.taskData.useAI = false;
      } else if (data === "upgrade") {
        await ctx.answerCbQuery();
        await ctx.reply("💎 Visit /upgrade to unlock premium features!");
        return;
      }

      await ctx.answerCbQuery();
    }

    // Show final confirmation
    const aiText = wizardState.taskData.useAI ?
      `🤖 AI Enhancement: ${wizardState.taskData.enhancementLevel}` :
      "🤖 AI Enhancement: None";

    const message = `
✅ *Task Ready to Create*

**Title:** ${wizardState.taskData.title}
**Description:** ${wizardState.taskData.description || "None"}
**Priority:** ${wizardState.taskData.priority || "medium"}
**Due Date:** ${wizardState.taskData.dueDate ? wizardState.taskData.dueDate.toLocaleDateString() : "None"}
${aiText}

Confirm to create this task?
    `;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("✅ Create Task", "confirm_create")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    // Handle final confirmation
    if (ctx.callbackQuery && "data" in ctx.callbackQuery && ctx.callbackQuery.data === "confirm_create") {
      await this.createTask(ctx, wizardState);
    }
  }

  /**
   * Create the task with all collected data
   */
  private async createTask(ctx: TBotContext, wizardState: TaskWizardSession): Promise<void> {
    try {
      await ctx.answerCbQuery();

      // Consume quotas
      await this.services.sessionManager.consumeQuota(ctx, "tasks");
      if (wizardState.taskData.useAI) {
        await this.services.sessionManager.consumeQuota(ctx, "aiRequests");
      }

      // Create task using the service
      const task = await this.services.taskService.createTask(
        ctx.from!.id.toString(),
        wizardState.taskData.title!,
        wizardState.taskData.description,
        wizardState.taskData.priority || "medium",
        wizardState.taskData.dueDate,
        {
          source: "wizard",
          useAI: wizardState.taskData.useAI,
          enhancementLevel: wizardState.taskData.enhancementLevel
        }
      );

      const successMessage = `
🎉 *Task Created Successfully!*

**${task.title}**
${task.description ? `\n${task.description}` : ""}

Priority: ${task.priority}
Status: ${task.status}
${task.dueDate ? `Due: ${new Date(task.dueDate).toLocaleDateString()}` : ""}

Use /tasks to view all your tasks.
      `;

      await ctx.reply(successMessage, { parse_mode: "Markdown" });

      // Update analytics
      await this.services.sessionManager.updateAnalytics(ctx, "task_created");

      return ctx.scene.leave();
    } catch (error: any) {
      console.error("Error creating task:", error);
      await ctx.reply("❌ Failed to create task. Please try again.");
      return ctx.scene.leave();
    }
  }

  /**
   * Handle cancellation
   */
  private async handleCancel(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    await ctx.reply("❌ Task creation cancelled.");
    return ctx.scene.leave();
  }

  /**
   * Handle back navigation
   */
  private async handleBack(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    return ctx.wizard.back();
  }

  /**
   * Skip description step
   */
  private async skipDescription(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery();
    return ctx.wizard.next();
  }

  /**
   * Skip due date step
   */
  private async skipDueDate(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery();
    return ctx.wizard.next();
  }

  /**
   * Parse due date from text (simplified implementation)
   */
  private parseDueDate(text: string): Date {
    const now = new Date();
    const lowerText = text.toLowerCase();

    if (lowerText === "tomorrow") {
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    }

    if (lowerText === "today") {
      return now;
    }

    // Try to parse as ISO date
    const isoDate = new Date(text);
    if (!isNaN(isoDate.getTime())) {
      return isoDate;
    }

    throw new Error("Invalid date format");
  }
}
