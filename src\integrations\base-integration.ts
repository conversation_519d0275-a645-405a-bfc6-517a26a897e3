/**
 * Base integration class providing common functionality for external service integrations
 */

export interface IntegrationConfig {
  apiKey?: string;
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
  rateLimitPerMinute?: number;
}

export interface IntegrationResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
  rateLimitRemaining?: number;
  rateLimitReset?: number;
}

export abstract class BaseIntegration {
  protected config: IntegrationConfig;
  protected lastRequestTime: number = 0;
  protected requestCount: number = 0;
  protected requestWindow: number = 60000; // 1 minute

  constructor(config: IntegrationConfig) {
    this.config = {
      timeout: 10000, // 10 seconds default
      retryAttempts: 3,
      rateLimitPerMinute: 60,
      ...config
    };
  }

  /**
   * Make HTTP request with retry logic and rate limiting
   */
  protected async makeRequest<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<IntegrationResponse<T>> {
    try {
      // Rate limiting check
      await this.checkRateLimit();

      // Set default headers
      const headers = {
        "Content-Type": "application/json",
        "User-Agent": "Novers-Telegram-Bot/1.0",
        ...options.headers
      };

      // Add API key if available
      if (this.config.apiKey) {
        (headers as any)["Authorization"] = `Bearer ${this.config.apiKey}`;
      }

      const requestOptions: RequestInit = {
        ...options,
        headers,
        signal: AbortSignal.timeout(this.config.timeout!)
      };

      let lastError: Error | null = null;

      // Retry logic
      for (let attempt = 1; attempt <= this.config.retryAttempts!; attempt++) {
        try {
          const response = await fetch(url, requestOptions);

          // Track rate limit headers if present
          const rateLimitRemaining = response.headers.get("X-RateLimit-Remaining");
          const rateLimitReset = response.headers.get("X-RateLimit-Reset");

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();

          return {
            success: true,
            data: data as T,
            statusCode: response.status,
            rateLimitRemaining: rateLimitRemaining ? parseInt(rateLimitRemaining) : undefined,
            rateLimitReset: rateLimitReset ? parseInt(rateLimitReset) : undefined
          };

        } catch (error) {
          lastError = error as Error;

          // Don't retry on certain errors
          if (error instanceof Error && error.name === "AbortError") {
            break;
          }

          // Wait before retry (exponential backoff)
          if (attempt < this.config.retryAttempts!) {
            await this.delay(Math.pow(2, attempt) * 1000);
          }
        }
      }

      return {
        success: false,
        error: lastError?.message || "Unknown error occurred",
        statusCode: 500
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
        statusCode: 500
      };
    }
  }

  /**
   * Check rate limiting
   */
  private async checkRateLimit(): Promise<void> {
    const now = Date.now();

    // Reset counter if window has passed
    if (now - this.lastRequestTime > this.requestWindow) {
      this.requestCount = 0;
      this.lastRequestTime = now;
    }

    // Check if we've exceeded rate limit
    if (this.requestCount >= this.config.rateLimitPerMinute!) {
      const waitTime = this.requestWindow - (now - this.lastRequestTime);
      if (waitTime > 0) {
        await this.delay(waitTime);
        this.requestCount = 0;
        this.lastRequestTime = Date.now();
      }
    }

    this.requestCount++;
  }

  /**
   * Delay utility
   */
  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate configuration
   */
  protected validateConfig(): void {
    if (!this.config.baseUrl) {
      throw new Error("Base URL is required for integration");
    }
  }

  /**
   * Get integration status
   */
  async getStatus(): Promise<IntegrationResponse<{ status: string; timestamp: string }>> {
    try {
      const response = await this.makeRequest(`${this.config.baseUrl}/health`);

      if (response.success) {
        return {
          success: true,
          data: {
            status: "healthy",
            timestamp: new Date().toISOString()
          }
        };
      }

      return {
        success: false,
        error: "Integration health check failed"
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Health check failed"
      };
    }
  }

  /**
   * Abstract method for integration-specific initialization
   */
  abstract initialize(): Promise<void>;

  /**
   * Abstract method for integration-specific cleanup
   */
  abstract cleanup(): Promise<void>;
}
