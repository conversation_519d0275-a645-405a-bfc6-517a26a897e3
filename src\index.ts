import { <PERSON><PERSON><PERSON> } from "@scalar/hono-api-reference";
import type { Context } from "hono";
import { Hono } from "hono";
import { describeRoute, openAPISpecs } from "hono-openapi";
import { resolver } from "hono-openapi/zod";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import * as z from "zod";
import { getConfig, validateConfig } from "./config";
import { authMiddleware } from "./middleware/auth.middleware";
import { errorHandler } from "./middleware/error.middleware";

// Import handlers
import birthdayHandler from "./handlers/birthday.handler";
import eventHandler from "./handlers/event.handler";
import notificationHandler from "./handlers/notification.handler";
import taskHandler from "./handlers/task.handler";

// Import Telegram bot
import { TelegramBot } from "./bot/telegram";

// Import scheduled event handler
import handleScheduledEvent from "./scheduled/notification-cron";
import { ErrorResponseSchema, HealthResponseSchema } from "./schemas";

const app = new Hono<{ Bindings: CloudflareBindings }>();

// Global middleware
app.use("*", logger());
app.use("*", cors());
app.use("*", errorHandler);

// Health check endpoint
app.get("/health",
  describeRoute({
    description: "Check the health of the service",
    responses: {
      200: {
        description: "Service is healthy",
        content: {
          "application/json": {
            schema: resolver(HealthResponseSchema)
          }
        }
      },
      500: {
        description: "Service is unhealthy",
        content: {
          "application/json": {
            schema: resolver(ErrorResponseSchema)
          }
        }
      }
    }
  }),
  (c) => {
    return c.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      environment: c.env.ENVIRONMENT || "development"
    });
  });

// Telegram webhook endpoint
app.post("/webhook/telegram", authMiddleware, async (c: Context) => {
  try {
    const config = getConfig(c.env);
    validateConfig(config);

    const telegramBot = new TelegramBot(c.env);
    return await telegramBot.handleWebhook(c);
  } catch (error: any) {
    console.error("Webhook error:", error);
    return c.json({ error: error.message }, 500);
  }
});

// API routes
app.route("/api/tasks", taskHandler);
app.route("/api/birthdays", birthdayHandler);
app.route("/api/events", eventHandler);
app.route("/api/notifications", notificationHandler);

// OpenAPI documentation
app.get(
  "/openapi",
  openAPISpecs(app, {
    documentation: {
      info: {
        title: "Novers Telegram Bot Assistant API",
        version: "1.0.0",
        description: "A comprehensive API for managing tasks, birthdays, Christian events, and notifications through a Telegram bot interface."
      },
      servers: [
        {
          url: "http://localhost:8787",
          description: "Local server"
        }
      ]
    },
    defaultOptions: {
      GET: {
        responses: {
          400: {
            description: "Zod Error",
            content: {
              "application/json": {
                schema: resolver(
                  z
                    .object({
                      status: z.literal(400),
                      message: z.string()
                    })
                )
              }
            }
          }
        }
      }
    }
  })
);

app.get(
  "/docs",
  Scalar({
    theme: "bluePlanet",
    url: "/openapi"
  })
);

// Export the main app and scheduled event handler
export default {
  fetch: app.fetch,
  scheduled: handleScheduledEvent
};
