// AI Service for intelligent task processing using Cloudflare Workers AI

export interface TaskExtractionResult {
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  category?: string;
  dueDate?: string; // ISO string
  reminderDate?: string; // ISO string
  tags?: string[];
  estimatedDuration?: number; // minutes
  confidence: number; // 0-1 score
  extractedEntities: {
    dates: string[];
    keywords: string[];
    urgencyIndicators: string[];
    actionVerbs: string[];
  };
}

export interface TaskEnhancementResult {
  enhancedTitle: string;
  enhancedDescription?: string;
  suggestedSubtasks?: string[];
  relatedTopics?: string[];
  improvementSuggestions?: string[];
}

export class AIService {
  private ai: Ai;

  constructor(ai: Ai) {
    this.ai = ai;
  }

  /**
   * Extract comprehensive task information from a message using AI
   */
  async extractTaskInformation(message: string): Promise<TaskExtractionResult> {
    try {
      const prompt = this.buildTaskExtractionPrompt(message);

      const response = await this.ai.run("@cf/meta/llama-3.1-8b-instruct", {
        messages: [
          {
            role: "system",
            content: "You are an expert task management assistant. Extract structured task information from user messages with high accuracy."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1024,
        temperature: 0.1 // Low temperature for consistent extraction
      });

      return this.parseTaskExtractionResponse(response.response || "", message);
    } catch (error) {
      console.error("AI task extraction failed:", error);
      return this.fallbackTaskExtraction(message);
    }
  }

  /**
   * Enhance and format a task using AI
   */
  async enhanceTask(title: string, description?: string): Promise<TaskEnhancementResult> {
    try {
      const prompt = this.buildTaskEnhancementPrompt(title, description);

      const response = await this.ai.run("@cf/meta/llama-3.1-8b-instruct", {
        messages: [
          {
            role: "system",
            content: "You are a productivity expert. Help improve task clarity, structure, and actionability."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1024,
        temperature: 0.3 // Slightly higher for creative enhancement
      });

      return this.parseTaskEnhancementResponse(response.response || "", title, description);
    } catch (error) {
      console.error("AI task enhancement failed:", error);
      return this.fallbackTaskEnhancement(title, description);
    }
  }

  /**
   * Categorize a task using AI
   */
  async categorizeTask(title: string, description?: string): Promise<string> {
    try {
      const prompt = this.buildCategorizationPrompt(title, description);

      const response = await this.ai.run("@cf/meta/llama-3.1-8b-instruct", {
        messages: [
          {
            role: "system",
            content: "You are a task categorization expert. Assign appropriate categories to tasks."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 100,
        temperature: 0.1
      });

      return this.parseCategorizationResponse(response.response || "");
    } catch (error) {
      console.error("AI categorization failed:", error);
      return this.fallbackCategorization(title, description);
    }
  }

  /**
   * Build prompt for task extraction
   */
  private buildTaskExtractionPrompt(message: string): string {
    return `
Extract task information from this message and respond with a JSON object:

Message: "${message}"

Extract the following information:
1. title: Clear, actionable task title (max 100 chars)
2. description: Detailed description if available
3. priority: "low", "medium", or "high" based on urgency indicators
4. category: General category (work, personal, health, etc.)
5. dueDate: ISO date string if mentioned (today, tomorrow, specific dates)
6. reminderDate: ISO date string for reminders
7. tags: Array of relevant tags
8. estimatedDuration: Estimated minutes to complete
9. confidence: Your confidence in extraction (0-1)
10. extractedEntities: Object with dates, keywords, urgencyIndicators, actionVerbs arrays

Respond only with valid JSON. Use current date context: ${new Date().toISOString().split("T")[0]}
`;
  }

  /**
   * Build prompt for task enhancement
   */
  private buildTaskEnhancementPrompt(title: string, description?: string): string {
    return `
Enhance this task for better clarity and actionability:

Title: "${title}"
Description: "${description || "None provided"}"

Provide improvements as JSON:
1. enhancedTitle: Clearer, more actionable title
2. enhancedDescription: Improved description with context
3. suggestedSubtasks: Array of 2-4 subtasks if applicable
4. relatedTopics: Array of related topics/areas
5. improvementSuggestions: Array of suggestions for better task management

Respond only with valid JSON.
`;
  }

  /**
   * Build prompt for categorization
   */
  private buildCategorizationPrompt(title: string, description?: string): string {
    return `
Categorize this task into one of these categories:
- Work
- Personal
- Health
- Finance
- Education
- Home
- Travel
- Shopping
- Social
- Other

Title: "${title}"
Description: "${description || "None"}"

Respond with only the category name.
`;
  }

  /**
   * Parse AI response for task extraction
   */
  private parseTaskExtractionResponse(response: string, originalMessage: string): TaskExtractionResult {
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const parsed = JSON.parse(jsonMatch[0]);

      // Validate and sanitize the response
      return {
        title: this.sanitizeString(parsed.title || this.extractTitleFallback(originalMessage)),
        description: parsed.description ? this.sanitizeString(parsed.description) : undefined,
        priority: this.validatePriority(parsed.priority),
        category: parsed.category ? this.sanitizeString(parsed.category) : undefined,
        dueDate: this.validateDate(parsed.dueDate),
        reminderDate: this.validateDate(parsed.reminderDate),
        tags: Array.isArray(parsed.tags) ? parsed.tags.map((tag: any) => this.sanitizeString(tag)).slice(0, 10) : [],
        estimatedDuration: typeof parsed.estimatedDuration === "number" ? Math.max(1, Math.min(parsed.estimatedDuration, 1440)) : undefined,
        confidence: typeof parsed.confidence === "number" ? Math.max(0, Math.min(parsed.confidence, 1)) : 0.5,
        extractedEntities: {
          dates: Array.isArray(parsed.extractedEntities?.dates) ? parsed.extractedEntities.dates.slice(0, 5) : [],
          keywords: Array.isArray(parsed.extractedEntities?.keywords) ? parsed.extractedEntities.keywords.slice(0, 10) : [],
          urgencyIndicators: Array.isArray(parsed.extractedEntities?.urgencyIndicators) ? parsed.extractedEntities.urgencyIndicators.slice(0, 5) : [],
          actionVerbs: Array.isArray(parsed.extractedEntities?.actionVerbs) ? parsed.extractedEntities.actionVerbs.slice(0, 5) : []
        }
      };
    } catch (error) {
      console.error("Failed to parse AI response:", error);
      return this.fallbackTaskExtraction(originalMessage);
    }
  }

  /**
   * Parse AI response for task enhancement
   */
  private parseTaskEnhancementResponse(response: string, originalTitle: string, originalDescription?: string): TaskEnhancementResult {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        enhancedTitle: this.sanitizeString(parsed.enhancedTitle || originalTitle),
        enhancedDescription: parsed.enhancedDescription ? this.sanitizeString(parsed.enhancedDescription) : originalDescription,
        suggestedSubtasks: Array.isArray(parsed.suggestedSubtasks) ? parsed.suggestedSubtasks.map((task: any) => this.sanitizeString(task)).slice(0, 5) : [],
        relatedTopics: Array.isArray(parsed.relatedTopics) ? parsed.relatedTopics.map((topic: any) => this.sanitizeString(topic)).slice(0, 5) : [],
        improvementSuggestions: Array.isArray(parsed.improvementSuggestions) ? parsed.improvementSuggestions.map((suggestion: any) => this.sanitizeString(suggestion)).slice(0, 3) : []
      };
    } catch (error) {
      console.error("Failed to parse enhancement response:", error);
      return this.fallbackTaskEnhancement(originalTitle, originalDescription);
    }
  }

  /**
   * Parse categorization response
   */
  private parseCategorizationResponse(response: string): string {
    const validCategories = ["Work", "Personal", "Health", "Finance", "Education", "Home", "Travel", "Shopping", "Social", "Other"];
    const category = response.trim();

    return validCategories.includes(category) ? category : "Other";
  }

  /**
   * Fallback task extraction when AI fails
   */
  private fallbackTaskExtraction(message: string): TaskExtractionResult {
    const lines = message.split("\n").filter(line => line.trim());
    const title = lines[0]?.substring(0, 100) || "New Task";
    const description = lines.length > 1 ? lines.slice(1).join("\n").substring(0, 500) : undefined;

    const lowerText = message.toLowerCase();
    let priority: "low" | "medium" | "high" = "medium";

    if (lowerText.includes("urgent") || lowerText.includes("asap") || lowerText.includes("important") || lowerText.includes("critical")) {
      priority = "high";
    } else if (lowerText.includes("low priority") || lowerText.includes("when possible") || lowerText.includes("someday")) {
      priority = "low";
    }

    return {
      title,
      description,
      priority,
      confidence: 0.3, // Low confidence for fallback
      extractedEntities: {
        dates: [],
        keywords: this.extractKeywords(message),
        urgencyIndicators: this.extractUrgencyIndicators(message),
        actionVerbs: this.extractActionVerbs(message)
      }
    };
  }

  /**
   * Fallback task enhancement
   */
  private fallbackTaskEnhancement(title: string, description?: string): TaskEnhancementResult {
    return {
      enhancedTitle: title,
      enhancedDescription: description,
      suggestedSubtasks: [],
      relatedTopics: [],
      improvementSuggestions: []
    };
  }

  /**
   * Fallback categorization
   */
  private fallbackCategorization(title: string, description?: string): string {
    const text = `${title} ${description || ""}`.toLowerCase();

    if (text.includes("work") || text.includes("meeting") || text.includes("project") || text.includes("deadline")) {
      return "Work";
    }
    if (text.includes("health") || text.includes("doctor") || text.includes("exercise") || text.includes("medical")) {
      return "Health";
    }
    if (text.includes("home") || text.includes("house") || text.includes("clean") || text.includes("repair")) {
      return "Home";
    }
    if (text.includes("shop") || text.includes("buy") || text.includes("purchase") || text.includes("store")) {
      return "Shopping";
    }
    if (text.includes("learn") || text.includes("study") || text.includes("course") || text.includes("education")) {
      return "Education";
    }
    if (text.includes("money") || text.includes("bank") || text.includes("pay") || text.includes("finance")) {
      return "Finance";
    }
    if (text.includes("travel") || text.includes("trip") || text.includes("vacation") || text.includes("flight")) {
      return "Travel";
    }
    if (text.includes("friend") || text.includes("family") || text.includes("social") || text.includes("party")) {
      return "Social";
    }

    return "Personal";
  }

  /**
   * Utility methods for data validation and sanitization
   */
  private sanitizeString(str: string): string {
    return str.trim().substring(0, 1000);
  }

  private validatePriority(priority: any): "low" | "medium" | "high" {
    return ["low", "medium", "high"].includes(priority) ? priority : "medium";
  }

  private validateDate(dateStr: any): string | undefined {
    if (!dateStr || typeof dateStr !== "string") {
      return undefined;
    }

    try {
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? undefined : date.toISOString();
    } catch {
      return undefined;
    }
  }

  private extractTitleFallback(message: string): string {
    const firstLine = message.split("\n")[0]?.trim();
    return firstLine?.substring(0, 100) || "New Task";
  }

  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase().match(/\b\w{3,}\b/g) || [];
    const commonWords = new Set(["the", "and", "for", "are", "but", "not", "you", "all", "can", "had", "her", "was", "one", "our", "out", "day", "get", "has", "him", "his", "how", "its", "may", "new", "now", "old", "see", "two", "way", "who", "boy", "did", "man", "end", "few", "got", "let", "put", "say", "she", "too", "use"]);

    return words
      .filter(word => !commonWords.has(word))
      .slice(0, 10);
  }

  private extractUrgencyIndicators(text: string): string[] {
    const urgencyWords = ["urgent", "asap", "important", "critical", "emergency", "immediate", "priority", "deadline", "rush"];
    const lowerText = text.toLowerCase();

    return urgencyWords.filter(word => lowerText.includes(word));
  }

  private extractActionVerbs(text: string): string[] {
    const actionVerbs = ["create", "make", "build", "write", "send", "call", "email", "buy", "fix", "clean", "organize", "plan", "schedule", "review", "update", "complete", "finish", "start", "begin", "prepare", "research", "analyze", "design", "implement", "test", "deploy"];
    const lowerText = text.toLowerCase();

    return actionVerbs.filter(verb => lowerText.includes(verb));
  }

  /**
   * Get AI processing statistics
   */
  async getProcessingStats(): Promise<{
    totalRequests: number;
    successRate: number;
    averageConfidence: number;
    lastProcessed: string;
  }> {
    // This would typically be stored in KV or database
    // For now, return mock data
    return {
      totalRequests: 0,
      successRate: 0.95,
      averageConfidence: 0.85,
      lastProcessed: new Date().toISOString()
    };
  }
}
