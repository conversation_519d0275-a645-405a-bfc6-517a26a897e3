import { BaseRepository } from "./base.repository";
import type { User } from "@/types";
import { DrizzleDB, users } from "../database/connection";
import { eq, lt, gte } from "drizzle-orm";

export class UserRepository extends BaseRepository<User> {
  constructor(db: DrizzleDB) {
    super(db);
  }

  // Find user by Telegram ID (same as findById since Telegram ID is the primary key)
  async findByTelegramId(telegramId: string): Promise<User | null> {
    return this.findById(telegramId);
  }

  // Find user by ID
  async findById(id: string): Promise<User | null> {
    try {
      const result = await this.db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);

      return result.length > 0 ? this.mapFromDb(result[0]) : null;
    } catch (error) {
      console.error("Error finding user by id:", error);
      throw error;
    }
  }

  // Create a new user
  async create(data: Partial<User>): Promise<User> {
    try {
      const mappedData = this.mapToDb(data);

      await this.db
        .insert(users)
        .values(mappedData as any); // Type assertion for now

      const created = await this.findById(mappedData.id);
      if (!created) {
        throw new Error("Failed to retrieve created user");
      }

      return created;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  // Update user's last active timestamp
  async updateLastActive(userId: string): Promise<void> {
    try {
      await this.db
        .update(users)
        .set({ lastActive: this.getCurrentTimestamp() })
        .where(eq(users.id, userId));
    } catch (error) {
      console.error("Error updating user last active:", error);
      throw error;
    }
  }

  // Update user preferences
  async updatePreferences(userId: string, preferences: Partial<User["preferences"]>): Promise<User | null> {
    try {
      const user = await this.findById(userId);
      if (!user) {
        return null;
      }

      const updatedPreferences = { ...user.preferences, ...preferences };

      await this.db
        .update(users)
        .set({ preferences: JSON.stringify(updatedPreferences) })
        .where(eq(users.id, userId));

      return await this.findById(userId);
    } catch (error) {
      console.error("Error updating user preferences:", error);
      throw error;
    }
  }

  // Get users who haven't been active for a specified number of days
  async findInactiveUsers(days: number): Promise<User[]> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const results = await this.db
        .select()
        .from(users)
        .where(lt(users.lastActive, cutoffDate.toISOString()));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding inactive users:", error);
      throw error;
    }
  }

  protected mapFromDb(row: any): User {
    return {
      id: row.id,
      username: row.username || undefined,
      firstName: row.firstName,
      lastName: row.lastName || undefined,
      createdAt: new Date(row.createdAt).toISOString(),
      lastActive: new Date(row.lastActive).toISOString(),
      preferences: row.preferences ? JSON.parse(row.preferences) : {
        timezone: "UTC",
        notificationTime: "09:00",
        mode: "chat" as const
      }
    };
  }

  protected mapToDb(data: Partial<User>): Record<string, any> {
    const mapped: Record<string, any> = {};

    if (data.id !== undefined) {
      mapped.id = data.id;
    }
    if (data.username !== undefined) {
      mapped.username = data.username;
    }
    if (data.firstName !== undefined) {
      mapped.firstName = data.firstName;
    }
    if (data.lastName !== undefined) {
      mapped.lastName = data.lastName;
    }
    if (data.createdAt !== undefined) {
      mapped.createdAt = data.createdAt;
    }
    if (data.lastActive !== undefined) {
      mapped.lastActive = data.lastActive;
    }
    if (data.preferences !== undefined) {
      mapped.preferences = JSON.stringify(data.preferences);
    }

    // Set defaults for new users
    if (!mapped.id) {
      mapped.id = this.generateId();
    }
    if (!mapped.createdAt) {
      mapped.createdAt = this.getCurrentTimestamp();
    }
    if (!mapped.lastActive) {
      mapped.lastActive = this.getCurrentTimestamp();
    }
    if (!mapped.preferences) {
      mapped.preferences = JSON.stringify({
        timezone: "UTC",
        notificationTime: "09:00",
        mode: "chat"
      });
    }

    return mapped;
  }

  // Find active users (users who have been active in the last 30 days)
  async findActiveUsers(): Promise<User[]> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const thirtyDaysAgoStr = thirtyDaysAgo.toISOString();

      const results = await this.db
        .select()
        .from(users)
        .where(gte(users.lastActive, thirtyDaysAgoStr))
        .execute();

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding active users:", error);
      throw error;
    }
  }
}
