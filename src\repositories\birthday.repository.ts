import { BaseRepository } from "./base.repository";
import type { Birthday } from "@/types";
import { DrizzleDB, birthdays } from "../database/connection";
import { eq, and, gte, lte, asc } from "drizzle-orm";

export class BirthdayRepository extends BaseRepository<Birthday> {
  constructor(db: DrizzleDB) {
    super(db);
  }

  // Find birthdays by user ID
  async findByUserId(userId: string): Promise<Birthday[]> {
    try {
      const results = await this.db
        .select()
        .from(birthdays)
        .where(and(eq(birthdays.userId, userId), eq(birthdays.active, true))!)
        .orderBy(asc(birthdays.name));

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding birthdays by user ID:", error);
      throw error;
    }
  }

  // Find upcoming birthdays within specified days
  async findUpcomingBirthdays(userId: string, days: number = 30): Promise<Birthday[]> {
    try {
      const today = new Date();

      // For simplicity, get all user birthdays and filter in memory
      // This could be optimized with more complex SQL queries later
      const allBirthdays = await this.findByUserId(userId);

      return allBirthdays.filter(birthday => {
        const daysUntil = this.calculateDaysUntilBirthday(
          { month: birthday.date.month, day: birthday.date.day },
          today
        );
        return daysUntil <= days;
      });
    } catch (error) {
      console.error("Error finding upcoming birthdays:", error);
      throw error;
    }
  }

  // Find birthdays for a specific date
  async findBirthdaysOnDate(userId: string, date: Date): Promise<Birthday[]> {
    try {
      const month = date.getMonth() + 1;
      const day = date.getDate();

      const results = await this.db
        .select()
        .from(birthdays)
        .where(
          and(
            eq(birthdays.userId, userId),
            eq(birthdays.active, true),
            eq(birthdays.month, month),
            eq(birthdays.day, day)
          )!
        );

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding birthdays for date:", error);
      throw error;
    }
  }

  // Create a new birthday
  async create(data: Partial<Birthday>): Promise<Birthday> {
    try {
      const mappedData = this.mapToDb(data);

      await this.db.insert(birthdays).values(mappedData);

      const result = await this.db
        .select()
        .from(birthdays)
        .where(eq(birthdays.id, mappedData.id))
        .limit(1);

      return this.mapFromDb(result[0]);
    } catch (error) {
      console.error("Error creating birthday:", error);
      throw error;
    }
  }

  // Find birthday by ID
  async findById(id: string): Promise<Birthday | null> {
    try {
      const result = await this.db
        .select()
        .from(birthdays)
        .where(eq(birthdays.id, id))
        .limit(1);

      return result.length > 0 ? this.mapFromDb(result[0]) : null;
    } catch (error) {
      console.error("Error finding birthday by ID:", error);
      throw error;
    }
  }

  // Update birthday
  async update(id: string, data: Partial<Birthday>): Promise<Birthday | null> {
    try {
      const mappedData = this.mapToDb(data);
      mappedData.updatedAt = this.getCurrentTimestamp();

      await this.db
        .update(birthdays)
        .set(mappedData)
        .where(eq(birthdays.id, id));

      return this.findById(id);
    } catch (error) {
      console.error("Error updating birthday:", error);
      throw error;
    }
  }

  // Delete birthday
  async delete(id: string): Promise<boolean> {
    try {
      await this.db
        .delete(birthdays)
        .where(eq(birthdays.id, id));

      return true;
    } catch (error) {
      console.error("Error deleting birthday:", error);
      throw error;
    }
  }

  // Find birthdays that need reminders today
  async findBirthdaysNeedingReminders(userId: string): Promise<{ birthday: Birthday; daysUntil: number }[]> {
    try {
      const birthdays = await this.findByUserId(userId);
      const today = new Date();
      const results: { birthday: Birthday; daysUntil: number }[] = [];

      for (const birthday of birthdays) {
        const daysUntil = this.calculateDaysUntilBirthday(birthday.date, today);

        // Check if any reminder day matches
        if (birthday.reminderDays.includes(daysUntil)) {
          results.push({ birthday, daysUntil });
        }
      }

      return results;
    } catch (error) {
      console.error("Error finding birthdays needing reminders:", error);
      throw error;
    }
  }

  // Calculate days until next birthday
  private calculateDaysUntilBirthday(birthdayDate: { month: number; day: number }, fromDate: Date): number {
    const today = new Date(fromDate);
    const currentYear = today.getFullYear();

    // Create birthday date for current year
    let nextBirthday = new Date(currentYear, birthdayDate.month - 1, birthdayDate.day);

    // If birthday has passed this year, use next year
    if (nextBirthday < today) {
      nextBirthday = new Date(currentYear + 1, birthdayDate.month - 1, birthdayDate.day);
    }

    // Calculate difference in days
    const diffTime = nextBirthday.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  // Toggle birthday active status
  async toggleActive(id: string): Promise<Birthday | null> {
    try {
      const birthday = await this.findById(id);
      if (!birthday) {
        return null;
      }

      const newActiveStatus = !birthday.isActive;

      await this.db
        .update(birthdays)
        .set({
          active: newActiveStatus,
          updatedAt: this.getCurrentTimestamp()
        })
        .where(eq(birthdays.id, id));

      return await this.findById(id);
    } catch (error) {
      console.error("Error toggling birthday active status:", error);
      throw error;
    }
  }

  protected mapFromDb(row: any): Birthday {
    return {
      id: row.id,
      userId: row.userId,
      name: row.name,
      date: {
        day: row.day,
        month: row.month
      },
      year: row.year || undefined,
      reminderDays: row.reminderDays ? JSON.parse(row.reminderDays) : [1, 7],
      isActive: Boolean(row.active),
      createdAt: row.createdAt,
      updatedAt: row.updatedAt
    };
  }

  protected mapToDb(data: Partial<Birthday>): any {
    const mapped: any = {};

    if (data.id !== undefined) {
      mapped.id = data.id;
    }
    if (data.userId !== undefined) {
      mapped.userId = data.userId;
    }
    if (data.name !== undefined) {
      mapped.name = data.name;
    }
    if (data.date !== undefined) {
      mapped.day = data.date.day;
      mapped.month = data.date.month;
    }
    if (data.year !== undefined) {
      mapped.year = data.year;
    }
    if (data.reminderDays !== undefined) {
      mapped.reminderDays = JSON.stringify(data.reminderDays);
    }
    if (data.createdAt !== undefined) {
      mapped.createdAt = data.createdAt;
    }
    if (data.updatedAt !== undefined) {
      mapped.updatedAt = data.updatedAt;
    }
    if (data.isActive !== undefined) {
      mapped.active = data.isActive;
    }

    // Set defaults for new birthdays
    if (!mapped.id) {
      mapped.id = this.generateId();
    }
    if (!mapped.createdAt) {
      mapped.createdAt = this.getCurrentTimestamp();
    }
    if (!mapped.updatedAt) {
      mapped.updatedAt = this.getCurrentTimestamp();
    }
    if (mapped.active === undefined) {
      mapped.active = true;
    }
    if (!mapped.reminderDays) {
      mapped.reminderDays = JSON.stringify([1, 7]);
    }
    if (!mapped.suggestions) {
      mapped.suggestions = JSON.stringify([]);
    }

    return mapped;
  }

  // Find upcoming birthdays for all users within specified days
  async findUpcoming(days: number = 30): Promise<Birthday[]> {
    try {
      const today = new Date();
      const currentMonth = today.getMonth() + 1; // JavaScript months are 0-based
      const currentDay = today.getDate();

      // Calculate end date
      const endDate = new Date(today);
      endDate.setDate(endDate.getDate() + days);
      const endMonth = endDate.getMonth() + 1;
      const endDay = endDate.getDate();

      let results;

      if (currentMonth === endMonth) {
        // Same month - simple range
        results = await this.db
          .select()
          .from(birthdays)
          .where(
            and(
              eq(birthdays.month, currentMonth),
              gte(birthdays.day, currentDay),
              lte(birthdays.day, endDay),
              eq(birthdays.active, true)
            )
          )
          .execute();
      } else {
        // Cross month boundary
        const currentMonthResults = await this.db
          .select()
          .from(birthdays)
          .where(
            and(
              eq(birthdays.month, currentMonth),
              gte(birthdays.day, currentDay),
              eq(birthdays.active, true)
            )
          )
          .execute();

        const nextMonthResults = await this.db
          .select()
          .from(birthdays)
          .where(
            and(
              eq(birthdays.month, endMonth),
              lte(birthdays.day, endDay),
              eq(birthdays.active, true)
            )
          )
          .execute();

        results = [...currentMonthResults, ...nextMonthResults];
      }

      return results.map(row => this.mapFromDb(row));
    } catch (error) {
      console.error("Error finding upcoming birthdays:", error);
      throw error;
    }
  }
}
