// Repository factory and exports

import { UserRepository } from "./user.repository";
import { TaskRepository } from "./task.repository";
import { BirthdayRepository } from "./birthday.repository";
import { EventRepository } from "./event.repository";
import { NotificationRepository } from "./notification.repository";
import { DatabaseInitializer } from "@/database/init";
import { DrizzleDB, createDrizzleDB } from "../database/connection";

export class RepositoryFactory {
  private drizzleDb: DrizzleDB;
  private userRepository?: UserRepository;
  private taskRepository?: TaskRepository;
  private birthdayRepository?: BirthdayRepository;
  private eventRepository?: EventRepository;
  private notificationRepository?: NotificationRepository;
  private databaseInitializer?: DatabaseInitializer;

  constructor(d1Database: D1Database) {
    this.drizzleDb = createDrizzleDB(d1Database);
  }

  // Lazy initialization of repositories
  getUserRepository(): UserRepository {
    if (!this.userRepository) {
      this.userRepository = new UserRepository(this.drizzleDb);
    }
    return this.userRepository;
  }

  getTaskRepository(): TaskRepository {
    if (!this.taskRepository) {
      this.taskRepository = new TaskRepository(this.drizzleDb);
    }
    return this.taskRepository;
  }

  getBirthdayRepository(): BirthdayRepository {
    if (!this.birthdayRepository) {
      this.birthdayRepository = new BirthdayRepository(this.drizzleDb);
    }
    return this.birthdayRepository;
  }

  getEventRepository(): EventRepository {
    if (!this.eventRepository) {
      this.eventRepository = new EventRepository(this.drizzleDb);
    }
    return this.eventRepository;
  }

  getNotificationRepository(): NotificationRepository {
    if (!this.notificationRepository) {
      this.notificationRepository = new NotificationRepository(this.drizzleDb);
    }
    return this.notificationRepository;
  }

  getDatabaseInitializer(): DatabaseInitializer {
    if (!this.databaseInitializer) {
      this.databaseInitializer = new DatabaseInitializer(this.drizzleDb.$client);
    }
    return this.databaseInitializer;
  }

  // Initialize database if needed
  async ensureInitialized(): Promise<void> {
    const initializer = this.getDatabaseInitializer();
    const isInitialized = await initializer.isInitialized();

    if (!isInitialized) {
      console.log("Database not initialized, initializing...");
      await initializer.initialize();
    }
  }
}

// Export all repositories and related classes
export { BaseRepository } from "./base.repository";
export { UserRepository } from "./user.repository";
export { TaskRepository } from "./task.repository";
export { BirthdayRepository } from "./birthday.repository";
export { EventRepository } from "./event.repository";
export { NotificationRepository } from "./notification.repository";
export { DatabaseInitializer } from "@/database/init";

// Utility function to create repository factory from environment
export function createRepositoryFactory(env: Env): RepositoryFactory {
  return new RepositoryFactory(env.BOT_DB);
}
